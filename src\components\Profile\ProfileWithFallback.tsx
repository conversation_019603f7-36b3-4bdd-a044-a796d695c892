import React, { Suspense } from 'react';
import styled from 'styled-components';
import { SimpleCard } from './SimpleCard';

const LoadingContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00ffff;
  font-family: 'Fira Code', monospace;
  flex-direction: column;
  gap: 1rem;
`;

const LoadingSpinner = styled.div`
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 255, 255, 0.3);
  border-top: 3px solid #00ffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorBoundary = class extends React.Component<
  { children: React.ReactNode; fallback: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; fallback: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.log('3D Component Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }

    return this.props.children;
  }
};

const LoadingFallback = () => (
  <LoadingContainer>
    <LoadingSpinner />
    <div>Loading 3D Profile Card...</div>
    <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>
      Initializing Three.js components
    </div>
  </LoadingContainer>
);

const ErrorFallback = () => (
  <div>
    <div style={{ 
      color: '#ffff00', 
      textAlign: 'center', 
      marginBottom: '1rem',
      fontSize: '0.9rem'
    }}>
      3D mode unavailable - using 2D card
    </div>
    <SimpleCard />
  </div>
);

export const ProfileWithFallback: React.FC = () => {
  // Lazy load the 3D component
  const WorkingCardApp = React.lazy(() =>
    import('./WorkingCard.jsx').then(module => ({ default: module.default }))
  );

  return (
    <ErrorBoundary fallback={<ErrorFallback />}>
      <Suspense fallback={<LoadingFallback />}>
        <WorkingCardApp />
      </Suspense>
    </ErrorBoundary>
  );
};
