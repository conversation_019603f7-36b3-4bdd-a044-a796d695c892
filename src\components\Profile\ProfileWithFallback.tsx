import React, { Suspense, useState } from 'react';
import styled from 'styled-components';
import { SimpleCard } from './SimpleCard';
import { IDCard } from './IDCard';

const ProfileContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
`;

const CardSelector = styled.div`
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
  z-index: 10;
`;

const SelectorButton = styled.button<{ active: boolean }>`
  padding: 8px 12px;
  background: ${props => props.active ? 'rgba(0, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.1)'};
  border: 1px solid ${props => props.active ? '#00ffff' : 'rgba(255, 255, 255, 0.3)'};
  border-radius: 6px;
  color: ${props => props.active ? '#00ffff' : '#ffffff'};
  font-family: 'Fira Code', monospace;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: #00ffff;
    color: #00ffff;
  }
`;

const LoadingContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00ffff;
  font-family: 'Fira Code', monospace;
  flex-direction: column;
  gap: 1rem;
`;

const LoadingSpinner = styled.div`
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 255, 255, 0.3);
  border-top: 3px solid #00ffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorBoundary = class extends React.Component<
  { children: React.ReactNode; fallback: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; fallback: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.log('3D Component Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }

    return this.props.children;
  }
};

const LoadingFallback = () => (
  <LoadingContainer>
    <LoadingSpinner />
    <div>Loading 3D Profile Card...</div>
    <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>
      Initializing Three.js components
    </div>
  </LoadingContainer>
);

const ErrorFallback = ({ cardType }: { cardType: 'id' | 'simple' }) => (
  <div>
    <div style={{
      color: '#ffff00',
      textAlign: 'center',
      marginBottom: '1rem',
      fontSize: '0.9rem'
    }}>
      3D mode unavailable - using {cardType === 'id' ? 'ID card' : '2D card'}
    </div>
    {cardType === 'id' ? <IDCard /> : <SimpleCard />}
  </div>
);

export const ProfileWithFallback: React.FC = () => {
  const [cardType, setCardType] = useState<'3d' | 'id' | 'simple'>('id');

  // Lazy load the 3D component
  const WorkingCardApp = React.lazy(() =>
    import('./WorkingCard.jsx').then(module => ({ default: module.default }))
  );

  const renderCard = () => {
    switch (cardType) {
      case '3d':
        return (
          <ErrorBoundary fallback={<ErrorFallback cardType="id" />}>
            <Suspense fallback={<LoadingFallback />}>
              <WorkingCardApp />
            </Suspense>
          </ErrorBoundary>
        );
      case 'id':
        return <IDCard />;
      case 'simple':
        return <SimpleCard />;
      default:
        return <IDCard />;
    }
  };

  return (
    <ProfileContainer>
      <CardSelector>
        <SelectorButton
          active={cardType === 'id'}
          onClick={() => setCardType('id')}
        >
          ID CARD
        </SelectorButton>
        <SelectorButton
          active={cardType === '3d'}
          onClick={() => setCardType('3d')}
        >
          3D CARD
        </SelectorButton>
        <SelectorButton
          active={cardType === 'simple'}
          onClick={() => setCardType('simple')}
        >
          2D CARD
        </SelectorButton>
      </CardSelector>

      {renderCard()}
    </ProfileContainer>
  );
};
