/* eslint-disable react/prop-types */
/* eslint-disable react/no-unknown-property */
import * as THREE from 'three'
import { useEffect, useRef, useState } from 'react'
import { Canvas, extend, useThree, useFrame } from '@react-three/fiber'
import { Text } from '@react-three/drei'
import { BallCollider, CuboidCollider, Physics, RigidBody, useRopeJoint, useSphericalJoint } from '@react-three/rapier'
import { MeshLineGeometry, MeshLineMaterial } from 'meshline'
import { personalInfo } from '../../data/portfolio'

extend({ MeshLineGeometry, MeshLineMaterial })

// Physics-based Badge Component
function Badge({ maxSpeed = 50, minSpeed = 10 }) {
  const band = useRef()
  const fixed = useRef()
  const j1 = useRef()
  const j2 = useRef()
  const j3 = useRef()
  const card = useRef()

  const vec = new THREE.Vector3()
  const ang = new THREE.Vector3()
  const rot = new THREE.Vector3()
  const dir = new THREE.Vector3()

  const [dragged, drag] = useState(false)
  const [hovered, hover] = useState(false)

  const { width, height } = useThree((state) => state.size)
  const [curve] = useState(() =>
    new THREE.CatmullRomCurve3([
      new THREE.Vector3(),
      new THREE.Vector3(),
      new THREE.Vector3(),
      new THREE.Vector3(),
    ])
  )

  // Connect band joints
  useRopeJoint(fixed, j1, [[0, 0, 0], [0, 0, 0], 1])
  useRopeJoint(j1, j2, [[0, 0, 0], [0, 0, 0], 1])
  useRopeJoint(j2, j3, [[0, 0, 0], [0, 0, 0], 1])
  useSphericalJoint(j3, card, [[0, 0, 0], [0, 1.45, 0]])

  useEffect(() => {
    if (hovered) {
      document.body.style.cursor = dragged ? 'grabbing' : 'grab'
      return () => void (document.body.style.cursor = 'auto')
    }
  }, [hovered, dragged])

  useFrame((state, delta) => {
    if (!fixed.current || !j1.current || !j2.current || !j3.current || !band.current || !card.current) return

    if (dragged) {
      vec.set(state.pointer.x, state.pointer.y, 0.5).unproject(state.camera)
      dir.copy(vec).sub(state.camera.position).normalize()
      vec.add(dir.multiplyScalar(state.camera.position.length()))
      ;[card, j1, j2, j3, fixed].forEach((ref) => ref.current?.wakeUp())
      card.current.setNextKinematicTranslation({
        x: vec.x - dragged.x,
        y: vec.y - dragged.y,
        z: vec.z - dragged.z,
      })
    }

    // Fix jitter
    const [j1Lerped, j2Lerped] = [j1, j2].map((ref) => {
      if (ref.current) {
        const lerped = new THREE.Vector3().copy(ref.current.translation())
        const clampedDistance = Math.max(0.1, Math.min(1, lerped.distanceTo(ref.current.translation())))
        return lerped.lerp(ref.current.translation(), delta * (minSpeed + clampedDistance * (maxSpeed - minSpeed)))
      }
    })

    // Calculate curve
    curve.points[0].copy(j3.current.translation())
    curve.points[1].copy(j2Lerped ?? j2.current.translation())
    curve.points[2].copy(j1Lerped ?? j1.current.translation())
    curve.points[3].copy(fixed.current.translation())
    band.current.geometry.setPoints(curve.getPoints(32))

    // Tilt card
    ang.copy(card.current.angvel())
    rot.copy(card.current.rotation())
    card.current.setAngvel({ x: ang.x, y: ang.y - rot.y * 0.25, z: ang.z }, false)
  })

  curve.curveType = 'chordal'
  const segmentProps = { type: 'dynamic', canSleep: true, colliders: false, angularDamping: 2, linearDamping: 2 }

  return (
    <>
      {/* Band */}
      <mesh ref={band}>
        <meshLineGeometry />
        <meshLineMaterial color="#333333" depthTest={false} resolution={[width, height]} lineWidth={2} />
      </mesh>

      {/* Physics bodies */}
      <group position={[0, 4, 0]}>
        <RigidBody ref={fixed} {...segmentProps} type="fixed" />
        <RigidBody position={[0.5, 0, 0]} ref={j1} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1, 0, 0]} ref={j2} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1.5, 0, 0]} ref={j3} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[2, 0, 0]} ref={card} {...segmentProps} type={dragged ? 'kinematicPosition' : 'dynamic'}>
          <CuboidCollider args={[0.8, 1.125, 0.01]} />
          <group
            scale={1.25}
            position={[0, -1.2, -0.05]}
            onPointerOver={() => hover(true)}
            onPointerOut={() => hover(false)}
            onPointerUp={() => drag(false)}
            onPointerDown={(evt) =>
              card.current && drag(new THREE.Vector3().copy(evt.point).sub(vec.copy(card.current.translation())))
            }
          >
            {/* Card */}
            <mesh>
              <boxGeometry args={[1.6, 2.25, 0.1]} />
              <meshStandardMaterial color="#ffffff" />

              {/* Card content */}
              <group position={[0, 0, 0.06]}>
                {/* Header */}
                <mesh position={[-0.5, 0.9, 0]}>
                  <boxGeometry args={[0.2, 0.2, 0.01]} />
                  <meshStandardMaterial color="#000000" />
                </mesh>

                <Text position={[-0.5, 0.9, 0.01]} fontSize={0.1} color="#ffffff" anchorX="center" anchorY="middle">G</Text>
                <Text position={[0.1, 0.9, 0]} fontSize={0.06} color="#000000" anchorX="center" anchorY="middle">gateramark</Text>

                {/* Photo */}
                <mesh position={[0, 0.2, 0]}>
                  <boxGeometry args={[0.8, 1.0, 0.01]} />
                  <meshStandardMaterial color="#f0f0f0" />
                </mesh>

                <Text position={[0, 0.2, 0.01]} fontSize={0.4} anchorX="center" anchorY="middle">👨‍💻</Text>

                {/* Info */}
                <Text position={[0, -0.4, 0]} fontSize={0.08} color="#000000" anchorX="center" anchorY="middle" maxWidth={1.4}>
                  {personalInfo.name}
                </Text>
                <Text position={[0, -0.6, 0]} fontSize={0.06} color="#666666" anchorX="center" anchorY="middle" maxWidth={1.4}>
                  {personalInfo.title}
                </Text>
                <Text position={[0, -0.8, 0]} fontSize={0.05} color="#888888" anchorX="center" anchorY="middle">
                  {personalInfo.yearsOfExperience}+ Years • {personalInfo.location}
                </Text>
              </group>
            </mesh>

            {/* Clip */}
            <mesh position={[0, 1.1, 0.06]}>
              <boxGeometry args={[0.3, 0.1, 0.05]} />
              <meshStandardMaterial color="#666666" metalness={0.8} roughness={0.2} />
            </mesh>
          </group>
        </RigidBody>
      </group>
    </>
  )
}

export default function WorkingCardApp() {
  return (
    <div style={{ width: '100%', height: '100%' }}>
      <Canvas camera={{ position: [0, 0, 13], fov: 25 }}>
        <ambientLight intensity={0.5} />
        <pointLight position={[10, 10, 10]} />
        <Physics gravity={[0, -40, 0]} timeStep={1 / 60}>
          <Badge />
        </Physics>
      </Canvas>
    </div>
  )
}


