/* eslint-disable react/prop-types */
/* eslint-disable react/no-unknown-property */
import * as THREE from 'three'
import { useEffect, useRef, useState } from 'react'
import { Canvas, extend, useThree, useFrame } from '@react-three/fiber'
import { Text } from '@react-three/drei'
import { BallCollider, CuboidCollider, Physics, RigidBody, useRopeJoint, useSphericalJoint } from '@react-three/rapier'
import { MeshLineGeometry, MeshLineMaterial } from 'meshline'
import { personalInfo } from '../../data/portfolio'

extend({ MeshLineGeometry, MeshLineMaterial })

// Simple working version first
function SimpleCard() {
  const meshRef = useRef()
  const [hovered, setHovered] = useState(false)

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime) * 0.1
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 2) * 0.1
    }
  })

  return (
    <group>
      {/* Lanyard */}
      <mesh position={[0, 2, 0]}>
        <cylinderGeometry args={[0.02, 0.02, 4]} />
        <meshStandardMaterial color="#333333" />
      </mesh>

      {/* Card */}
      <mesh
        ref={meshRef}
        position={[0, -1, 0]}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
        scale={hovered ? 1.1 : 1}
      >
        <boxGeometry args={[1.6, 2.4, 0.1]} />
        <meshStandardMaterial color="#e8e8e8" />

        {/* Card Content */}
        <group position={[0, 0, 0.06]}>
          {/* Header */}
          <mesh position={[0, 1.0, 0]}>
            <boxGeometry args={[1.58, 0.15, 0.01]} />
            <meshStandardMaterial color="#000000" />
          </mesh>

          {/* Company logo "G" */}
          <mesh position={[-0.6, 1.0, 0.01]}>
            <boxGeometry args={[0.12, 0.12, 0.01]} />
            <meshStandardMaterial color="#ffffff" />
          </mesh>

          <Text
            position={[-0.6, 1.0, 0.02]}
            fontSize={0.08}
            color="#000000"
            anchorX="center"
            anchorY="middle"
          >
            G
          </Text>

          {/* Company name */}
          <Text
            position={[0.1, 1.0, 0.01]}
            fontSize={0.05}
            color="#ffffff"
            anchorX="center"
            anchorY="middle"
          >
            gateramark
          </Text>

          {/* Photo area */}
          <mesh position={[0, 0.3, 0]}>
            <boxGeometry args={[0.7, 0.9, 0.01]} />
            <meshStandardMaterial color="#f5f5f5" />
          </mesh>

          {/* Photo border */}
          <mesh position={[0, 0.3, 0.01]}>
            <boxGeometry args={[0.68, 0.88, 0.01]} />
            <meshStandardMaterial color="#ffffff" />
          </mesh>

          {/* Geometric shapes */}
          <Text
            position={[-0.12, 0.4, 0.02]}
            fontSize={0.08}
            color="#cccccc"
            anchorX="center"
            anchorY="middle"
          >
            ⬜
          </Text>
          <Text
            position={[0, 0.4, 0.02]}
            fontSize={0.08}
            color="#cccccc"
            anchorX="center"
            anchorY="middle"
          >
            ⬜
          </Text>
          <Text
            position={[0.12, 0.4, 0.02]}
            fontSize={0.08}
            color="#cccccc"
            anchorX="center"
            anchorY="middle"
          >
            ⬜
          </Text>

          {/* Name */}
          <Text
            position={[0, -0.3, 0]}
            fontSize={0.07}
            color="#000000"
            anchorX="center"
            anchorY="middle"
            maxWidth={1.4}
          >
            {personalInfo.name}
          </Text>

          {/* Additional info */}
          <Text
            position={[0, -0.5, 0]}
            fontSize={0.04}
            color="#666666"
            anchorX="center"
            anchorY="middle"
          >
            {personalInfo.yearsOfExperience}+ Years • {personalInfo.location}
          </Text>
        </group>
      </mesh>

      {/* Clip */}
      <mesh position={[0, 0.2, 0.06]}>
        <boxGeometry args={[0.2, 0.1, 0.05]} />
        <meshStandardMaterial color="#666666" metalness={0.8} roughness={0.2} />
      </mesh>
    </group>
  )
}

// Physics-based Badge Component
function Badge({ maxSpeed = 50, minSpeed = 10 }) {
  const band = useRef()
  const fixed = useRef()
  const j1 = useRef()
  const j2 = useRef()
  const j3 = useRef()
  const card = useRef()

  const vec = new THREE.Vector3()
  const ang = new THREE.Vector3()
  const rot = new THREE.Vector3()
  const dir = new THREE.Vector3()

  const [dragged, drag] = useState(false)
  const [hovered, hover] = useState(false)

  const { width, height } = useThree((state) => state.size)
  const [curve] = useState(() =>
    new THREE.CatmullRomCurve3([
      new THREE.Vector3(),
      new THREE.Vector3(),
      new THREE.Vector3(),
      new THREE.Vector3(),
    ])
  )

  // Connect band joints
  useRopeJoint(fixed, j1, [[0, 0, 0], [0, 0, 0], 1])
  useRopeJoint(j1, j2, [[0, 0, 0], [0, 0, 0], 1])
  useRopeJoint(j2, j3, [[0, 0, 0], [0, 0, 0], 1])
  useSphericalJoint(j3, card, [[0, 0, 0], [0, 1.45, 0]])

  useEffect(() => {
    if (hovered) {
      document.body.style.cursor = dragged ? 'grabbing' : 'grab'
      return () => void (document.body.style.cursor = 'auto')
    }
  }, [hovered, dragged])

  useFrame((state, delta) => {
    if (!fixed.current || !j1.current || !j2.current || !j3.current || !band.current || !card.current) return

    if (dragged) {
      vec.set(state.pointer.x, state.pointer.y, 0.5).unproject(state.camera)
      dir.copy(vec).sub(state.camera.position).normalize()
      vec.add(dir.multiplyScalar(state.camera.position.length()))
      ;[card, j1, j2, j3, fixed].forEach((ref) => ref.current?.wakeUp())
      card.current.setNextKinematicTranslation({
        x: vec.x - dragged.x,
        y: vec.y - dragged.y,
        z: vec.z - dragged.z,
      })
    }

    // Fix jitter
    const [j1Lerped, j2Lerped] = [j1, j2].map((ref) => {
      if (ref.current) {
        const lerped = new THREE.Vector3().copy(ref.current.translation())
        const clampedDistance = Math.max(0.1, Math.min(1, lerped.distanceTo(ref.current.translation())))
        return lerped.lerp(ref.current.translation(), delta * (minSpeed + clampedDistance * (maxSpeed - minSpeed)))
      }
    })

    // Calculate curve
    curve.points[0].copy(j3.current.translation())
    curve.points[1].copy(j2Lerped ?? j2.current.translation())
    curve.points[2].copy(j1Lerped ?? j1.current.translation())
    curve.points[3].copy(fixed.current.translation())
    band.current.geometry.setPoints(curve.getPoints(32))

    // Tilt card
    ang.copy(card.current.angvel())
    rot.copy(card.current.rotation())
    card.current.setAngvel({ x: ang.x, y: ang.y - rot.y * 0.25, z: ang.z }, false)
  })

  curve.curveType = 'chordal'
  const segmentProps = { type: 'dynamic', canSleep: true, colliders: false, angularDamping: 2, linearDamping: 2 }

  return (
    <>
      {/* Lanyard */}
      <mesh ref={band}>
        <meshLineGeometry />
        <meshLineMaterial color="#1a1a1a" depthTest={false} resolution={[width, height]} lineWidth={3} />
      </mesh>

      {/* Physics bodies */}
      <group position={[0, 4, 0]}>
        <RigidBody ref={fixed} {...segmentProps} type="fixed" />
        <RigidBody position={[0.5, 0, 0]} ref={j1} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1, 0, 0]} ref={j2} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1.5, 0, 0]} ref={j3} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[2, 0, 0]} ref={card} {...segmentProps} type={dragged ? 'kinematicPosition' : 'dynamic'}>
          <CuboidCollider args={[0.8, 1.125, 0.01]} />
          <group
            scale={2.25}
            position={[0, -1.2, -0.05]}
            onPointerOver={(e) => {
              e.stopPropagation()
              hover(true)
            }}
            onPointerOut={(e) => {
              e.stopPropagation()
              hover(false)
            }}
            onPointerUp={(e) => {
              e.stopPropagation()
              if (e.target.releasePointerCapture) e.target.releasePointerCapture(e.pointerId)
              drag(false)
            }}
            onPointerDown={(e) => {
              e.stopPropagation()
              if (e.target.setPointerCapture) e.target.setPointerCapture(e.pointerId)
              if (card.current) {
                drag(new THREE.Vector3().copy(e.point).sub(vec.copy(card.current.translation())))
              }
            }}
          >
            {/* Main ID Card */}
            <mesh>
              <boxGeometry args={[0.8, 1.2, 0.05]} />
              <meshStandardMaterial
                color="#e8e8e8"
                roughness={0.1}
                metalness={0.1}
              />
            </mesh>

            {/* Card Content */}
            <group position={[0, 0, 0.026]}>
              {/* Header bar */}
              <mesh position={[0, 0.5, 0]}>
                <boxGeometry args={[0.78, 0.08, 0.001]} />
                <meshStandardMaterial color="#000000" />
              </mesh>

              {/* Company logo "G" */}
              <mesh position={[-0.28, 0.5, 0.001]}>
                <boxGeometry args={[0.06, 0.06, 0.001]} />
                <meshStandardMaterial color="#ffffff" />
              </mesh>

              <Text
                position={[-0.28, 0.5, 0.002]}
                fontSize={0.04}
                color="#000000"
                anchorX="center"
                anchorY="middle"
              >
                G
              </Text>

              {/* Company name */}
              <Text
                position={[0.1, 0.5, 0.001]}
                fontSize={0.025}
                color="#ffffff"
                anchorX="center"
                anchorY="middle"
              >
                gateramark
              </Text>

              {/* Photo area */}
              <mesh position={[0, 0.15, 0]}>
                <boxGeometry args={[0.35, 0.45, 0.001]} />
                <meshStandardMaterial color="#f5f5f5" />
              </mesh>

              {/* Photo border */}
              <mesh position={[0, 0.15, 0.001]}>
                <boxGeometry args={[0.33, 0.43, 0.001]} />
                <meshStandardMaterial color="#ffffff" />
              </mesh>

              {/* Geometric shapes placeholder */}
              <Text
                position={[-0.06, 0.2, 0.002]}
                fontSize={0.04}
                color="#cccccc"
                anchorX="center"
                anchorY="middle"
              >
                ⬜
              </Text>
              <Text
                position={[0, 0.2, 0.002]}
                fontSize={0.04}
                color="#cccccc"
                anchorX="center"
                anchorY="middle"
              >
                ⬜
              </Text>
              <Text
                position={[0.06, 0.2, 0.002]}
                fontSize={0.04}
                color="#cccccc"
                anchorX="center"
                anchorY="middle"
              >
                ⬜
              </Text>

              {/* Name */}
              <Text
                position={[0, -0.15, 0]}
                fontSize={0.035}
                color="#000000"
                anchorX="center"
                anchorY="middle"
                maxWidth={0.7}
              >
                {personalInfo.name}
              </Text>

              {/* Additional info */}
              <Text
                position={[0, -0.25, 0]}
                fontSize={0.02}
                color="#666666"
                anchorX="center"
                anchorY="middle"
                maxWidth={0.7}
              >
                {personalInfo.yearsOfExperience}+ Years • {personalInfo.location}
              </Text>
            </group>

            {/* Hook/Clip system */}
            <group position={[0, 0.6, 0]}>
              {/* Main clip body */}
              <mesh position={[0, 0.05, 0.03]}>
                <boxGeometry args={[0.12, 0.08, 0.04]} />
                <meshStandardMaterial color="#2a2a2a" metalness={0.9} roughness={0.1} />
              </mesh>

              {/* Clip hole */}
              <mesh position={[0, 0.05, 0.05]}>
                <cylinderGeometry args={[0.015, 0.015, 0.06]} />
                <meshStandardMaterial color="#000000" />
              </mesh>

              {/* Hook - simplified */}
              <mesh position={[0, 0.08, 0.03]}>
                <boxGeometry args={[0.04, 0.02, 0.02]} />
                <meshStandardMaterial color="#1a1a1a" metalness={0.8} roughness={0.2} />
              </mesh>
            </group>
          </group>
        </RigidBody>
      </group>
    </>
  )
}

export default function WorkingCardApp() {
  return (
    <div style={{ width: '100%', height: '100%' }}>
      <Canvas camera={{ position: [0, 0, 13], fov: 25 }}>
        <color attach="background" args={['#0a0a0a']} />
        <ambientLight intensity={0.4} />
        <directionalLight position={[10, 10, 5]} intensity={1} />
        <pointLight position={[-10, -10, -5]} intensity={0.5} color="#4a90e2" />
        <Physics gravity={[0, -40, 0]} timeStep={1 / 60}>
          <Badge />
        </Physics>
      </Canvas>
    </div>
  )
}


