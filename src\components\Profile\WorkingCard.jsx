/* eslint-disable react/prop-types */
/* eslint-disable react/no-unknown-property */
import * as THREE from 'three'
import { useEffect, useRef, useState } from 'react'
import { Canvas, extend, useThree, useFrame } from '@react-three/fiber'
import { Environment, Lightformer, Text, RenderTexture, PerspectiveCamera } from '@react-three/drei'
import { BallCollider, CuboidCollider, Physics, RigidBody, useRopeJoint, useSphericalJoint } from '@react-three/rapier'
import { MeshLineGeometry, MeshLineMaterial } from 'meshline'
import { personalInfo } from '../../data/portfolio'

extend({ MeshLineGeometry, MeshLineMaterial })

// Custom badge content component
function BadgeContent() {
  return (
    <>
      <PerspectiveCamera makeDefault manual aspect={1.05} position={[0, 0, 2]} />

      {/* Background - Dark professional */}
      <mesh>
        <planeGeometry args={[2, 2.5]} />
        <meshBasicMaterial color="#0a0a0a" />
      </mesh>

      {/* Header section with logo and company name */}
      <mesh position={[-0.6, 1.0, 0.01]}>
        <boxGeometry args={[0.25, 0.25, 0.01]} />
        <meshBasicMaterial color="#ffffff" />
      </mesh>

      {/* Company logo "G" */}
      <Text
        position={[-0.6, 1.0, 0.02]}
        fontSize={0.15}
        color="#000000"
        anchorX="center"
        anchorY="middle"
        font="/fonts/inter-bold.woff"
      >
        G
      </Text>

      {/* Company name "gateramark" */}
      <Text
        position={[0.1, 1.0, 0.01]}
        fontSize={0.08}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
        font="/fonts/inter-regular.woff"
      >
        gateramark
      </Text>

      {/* Profile photo area - larger and more prominent */}
      <mesh position={[0, 0.2, 0.01]}>
        <boxGeometry args={[0.8, 1.0, 0.01]} />
        <meshBasicMaterial color="#2a2a2a" />
      </mesh>

      {/* Profile photo border */}
      <mesh position={[0, 0.2, 0.02]}>
        <boxGeometry args={[0.75, 0.95, 0.01]} />
        <meshBasicMaterial color="#ffffff" />
      </mesh>

      {/* Profile image placeholder */}
      <mesh position={[0, 0.2, 0.03]}>
        <boxGeometry args={[0.7, 0.9, 0.01]} />
        <meshBasicMaterial color="#1a1a1a" />
      </mesh>

      {/* Profile Emoji - positioned in photo area */}
      <Text
        position={[0, 0.2, 0.04]}
        fontSize={0.6}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
      >
        👨‍💻
      </Text>

      {/* Name - below photo */}
      <Text
        position={[0, -0.5, 0.01]}
        fontSize={0.1}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
        maxWidth={1.8}
        font="/fonts/inter-bold.woff"
      >
        {personalInfo.name}
      </Text>

      {/* Title */}
      <Text
        position={[0, -0.65, 0.01]}
        fontSize={0.07}
        color="#cccccc"
        anchorX="center"
        anchorY="middle"
        maxWidth={1.8}
        font="/fonts/inter-regular.woff"
      >
        {personalInfo.title}
      </Text>

      {/* ID Number or additional info */}
      <Text
        position={[0, -0.8, 0.01]}
        fontSize={0.05}
        color="#888888"
        anchorX="center"
        anchorY="middle"
        font="/fonts/inter-regular.woff"
      >
        ID: {new Date().getFullYear()}-{Math.floor(Math.random() * 1000).toString().padStart(3, '0')}
      </Text>

      {/* Department/Experience */}
      <Text
        position={[0, -0.95, 0.01]}
        fontSize={0.05}
        color="#888888"
        anchorX="center"
        anchorY="middle"
        font="/fonts/inter-regular.woff"
      >
        {personalInfo.yearsOfExperience}+ Years • {personalInfo.location}
      </Text>
    </>
  )
}

function Band({ maxSpeed = 50, minSpeed = 10 }) {
  const band = useRef(), fixed = useRef(), j1 = useRef(), j2 = useRef(), j3 = useRef(), card = useRef()
  const vec = new THREE.Vector3(), ang = new THREE.Vector3(), rot = new THREE.Vector3(), dir = new THREE.Vector3()
  const segmentProps = { type: 'dynamic', canSleep: true, colliders: false, angularDamping: 2, linearDamping: 2 }
  
  const { width, height } = useThree((state) => state.size)
  const [curve] = useState(() => new THREE.CatmullRomCurve3([new THREE.Vector3(), new THREE.Vector3(), new THREE.Vector3(), new THREE.Vector3()]))
  const [dragged, drag] = useState(false)
  const [hovered, hover] = useState(false)

  useRopeJoint(fixed, j1, [[0, 0, 0], [0, 0, 0], 1])
  useRopeJoint(j1, j2, [[0, 0, 0], [0, 0, 0], 1])
  useRopeJoint(j2, j3, [[0, 0, 0], [0, 0, 0], 1])
  useSphericalJoint(j3, card, [[0, 0, 0], [0, 1.45, 0]])

  useEffect(() => {
    if (hovered) {
      document.body.style.cursor = dragged ? 'grabbing' : 'grab'
      return () => void (document.body.style.cursor = 'auto')
    }
  }, [hovered, dragged])

  useFrame((state, delta) => {
    if (dragged) {
      vec.set(state.pointer.x, state.pointer.y, 0.5).unproject(state.camera)
      dir.copy(vec).sub(state.camera.position).normalize()
      vec.add(dir.multiplyScalar(state.camera.position.length()))
      ;[card, j1, j2, j3, fixed].forEach((ref) => ref.current?.wakeUp())
      card.current?.setNextKinematicTranslation({ x: vec.x - dragged.x, y: vec.y - dragged.y, z: vec.z - dragged.z })
    }
    if (fixed.current) {
      // Fix most of the jitter when over pulling the card
      ;[j1, j2].forEach((ref) => {
        if (!ref.current.lerped) ref.current.lerped = new THREE.Vector3().copy(ref.current.translation())
        const clampedDistance = Math.max(0.1, Math.min(1, ref.current.lerped.distanceTo(ref.current.translation())))
        ref.current.lerped.lerp(ref.current.translation(), delta * (minSpeed + clampedDistance * (maxSpeed - minSpeed)))
      })
      // Calculate catmul curve
      curve.points[0].copy(j3.current.translation())
      curve.points[1].copy(j2.current.lerped)
      curve.points[2].copy(j1.current.lerped)
      curve.points[3].copy(fixed.current.translation())
      band.current.geometry.setPoints(curve.getPoints(32))
      // Tilt it back towards the screen
      ang.copy(card.current.angvel())
      rot.copy(card.current.rotation())
      card.current.setAngvel({ x: ang.x, y: ang.y - rot.y * 0.25, z: ang.z })
    }
  })

  curve.curveType = 'chordal'

  return (
    <>
      <group position={[0, 4, 0]}>
        <RigidBody ref={fixed} {...segmentProps} type="fixed" />
        <RigidBody position={[0.5, 0, 0]} ref={j1} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1, 0, 0]} ref={j2} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1.5, 0, 0]} ref={j3} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[2, 0, 0]} ref={card} {...segmentProps} type={dragged ? 'kinematicPosition' : 'dynamic'}>
          <CuboidCollider args={[0.8, 1.125, 0.01]} />
          <group
            scale={1.25}
            position={[0, -1.2, -0.05]}
            onPointerOver={() => hover(true)}
            onPointerOut={() => hover(false)}
            onPointerUp={(e) => (e.target.releasePointerCapture(e.pointerId), drag(false))}
            onPointerDown={(e) => (e.target.setPointerCapture(e.pointerId), drag(new THREE.Vector3().copy(e.point).sub(vec.copy(card.current.translation()))))}
          >
            {/* Main Card */}
            <mesh>
              <boxGeometry args={[1.6, 2.25, 0.1]} />
              <meshPhysicalMaterial
                clearcoat={1}
                clearcoatRoughness={0.1}
                roughness={0.1}
                metalness={0.1}
                color="#ffffff"
              >
                <RenderTexture attach="map" height={1024} width={1024}>
                  <BadgeContent />
                </RenderTexture>
              </meshPhysicalMaterial>
            </mesh>

            {/* Card Border - subtle dark border */}
            <mesh position={[0, 0, -0.02]}>
              <boxGeometry args={[1.65, 2.3, 0.08]} />
              <meshStandardMaterial
                color="#1a1a1a"
                roughness={0.2}
                metalness={0.1}
              />
            </mesh>
            
            {/* Clip/Holder - more professional */}
            <mesh position={[0, 1.1, 0.06]}>
              <boxGeometry args={[0.4, 0.15, 0.08]} />
              <meshStandardMaterial color="#2a2a2a" metalness={0.9} roughness={0.1} />
            </mesh>

            {/* Clip hole */}
            <mesh position={[0, 1.1, 0.1]}>
              <cylinderGeometry args={[0.05, 0.05, 0.1]} />
              <meshStandardMaterial color="#000000" />
            </mesh>
          </group>
        </RigidBody>
      </group>
      
      {/* Lanyard */}
      <mesh ref={band}>
        <meshLineGeometry />
        <meshLineMaterial 
          color="#666666" 
          depthTest={false} 
          resolution={[width, height]} 
          lineWidth={2} 
        />
      </mesh>
    </>
  )
}

export default function WorkingCardApp() {
  return (
    <div style={{ width: '100%', height: '100%' }}>
      <Canvas camera={{ position: [0, 0, 13], fov: 25 }}>
        <ambientLight intensity={Math.PI} />
        <Physics interpolate gravity={[0, -40, 0]} timeStep={1 / 60}>
          <Band />
        </Physics>
        <Environment background blur={0.75}>
          <color attach="background" args={['#0a0a0a']} />
          <Lightformer intensity={2} color="white" position={[0, -1, 5]} rotation={[0, 0, Math.PI / 3]} scale={[100, 0.1, 1]} />
          <Lightformer intensity={3} color="white" position={[-1, -1, 1]} rotation={[0, 0, Math.PI / 3]} scale={[100, 0.1, 1]} />
          <Lightformer intensity={3} color="white" position={[1, 1, 1]} rotation={[0, 0, Math.PI / 3]} scale={[100, 0.1, 1]} />
          <Lightformer intensity={10} color="white" position={[-10, 0, 14]} rotation={[0, Math.PI / 2, Math.PI / 3]} scale={[100, 10, 1]} />
        </Environment>
      </Canvas>
    </div>
  )
}
