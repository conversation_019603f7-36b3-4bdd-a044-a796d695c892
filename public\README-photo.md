# How to Add Your Profile Photo

## Steps:

1. **Save your photo** as `profile-photo.jpg` in the `public` folder (replace the existing placeholder file)

2. **Update the ProfilePhoto component** in `src/components/Profile/WorkingCard.jsx`:

Replace the current ProfilePhoto function with:

```javascript
// Photo component with texture
function ProfilePhoto() {
  const texture = useTexture('/profile-photo.jpg')
  
  return (
    <mesh position={[0, 0.15, 0.002]}>
      <planeGeometry args={[0.33, 0.43]} />
      <meshStandardMaterial map={texture} />
    </mesh>
  )
}
```

3. **Make sure useTexture is imported** at the top of the file:
```javascript
import { Text, useTexture } from '@react-three/drei'
```

## Photo Requirements:
- Format: JPG, PNG, or WebP
- Recommended size: 400x500 pixels (4:5 aspect ratio)
- File name: `profile-photo.jpg`
- Location: `public/profile-photo.jpg`

The photo will automatically be cropped to fit the ID card dimensions.
