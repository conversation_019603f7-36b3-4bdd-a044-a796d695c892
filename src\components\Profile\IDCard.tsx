import React from 'react';
import styled, { keyframes } from 'styled-components';
import { motion } from 'framer-motion';
import { personalInfo, contactInfo } from '../../data/portfolio';

// Hanging animation keyframes
const hangingMotion = keyframes`
  0%, 100% { 
    transform: rotate(-2deg) translateY(0px);
  }
  25% { 
    transform: rotate(1deg) translateY(-3px);
  }
  50% { 
    transform: rotate(-1deg) translateY(-1px);
  }
  75% { 
    transform: rotate(0.5deg) translateY(-2px);
  }
`;

const lanyardSway = keyframes`
  0%, 100% { 
    transform: rotate(-1deg);
  }
  50% { 
    transform: rotate(1deg);
  }
`;

// Container for the entire ID card setup
const IDCardContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  perspective: 1000px;
  position: relative;
`;

// Lanyard/Rope component
const Lanyard = styled.div`
  width: 4px;
  height: 120px;
  background: linear-gradient(to bottom, #333, #666, #333);
  border-radius: 2px;
  position: relative;
  margin-bottom: 10px;
  animation: ${lanyardSway} 4s ease-in-out infinite;
  box-shadow: 
    inset 1px 0 0 rgba(255, 255, 255, 0.3),
    inset -1px 0 0 rgba(0, 0, 0, 0.3);

  &::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 8px;
    background: #444;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
`;

// Text on the lanyard
const LanyardText = styled.div`
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%) rotate(90deg);
  font-size: 8px;
  font-weight: bold;
  color: #00ffff;
  text-shadow: 0 0 3px rgba(0, 255, 255, 0.5);
  letter-spacing: 1px;
  white-space: nowrap;
`;

// Main ID card component
const Card = styled(motion.div)`
  width: 320px;
  height: 200px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 
    0 15px 35px rgba(0, 0, 0, 0.5),
    0 5px 15px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 0 20px rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.2);
  position: relative;
  animation: ${hangingMotion} 6s ease-in-out infinite;
  cursor: pointer;
  transform-origin: top center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(0, 255, 255, 0.05) 50%, transparent 70%);
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 1;
  }

  &:hover {
    animation-play-state: paused;
    transform: rotate(0deg) translateY(-5px) scale(1.02);
    box-shadow: 
      0 20px 40px rgba(0, 0, 0, 0.6),
      0 8px 20px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      0 0 30px rgba(0, 255, 255, 0.2);
  }
`;

// Header section with company/organization info
const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
`;

const CompanyLogo = styled.div`
  width: 30px;
  height: 30px;
  background: linear-gradient(135deg, #00ffff, #0080ff);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: #000;
  box-shadow: 0 2px 8px rgba(0, 255, 255, 0.3);
`;

const IDNumber = styled.div`
  font-size: 10px;
  color: #00ffff;
  opacity: 0.8;
  font-family: 'Fira Code', monospace;
`;

// Main content area
const CardContent = styled.div`
  display: flex;
  gap: 15px;
  height: calc(100% - 50px);
`;

// Profile photo section
const PhotoSection = styled.div`
  flex-shrink: 0;
`;

const ProfilePhoto = styled.div`
  width: 80px;
  height: 100px;
  background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  box-shadow: 
    0 4px 12px rgba(0, 255, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: rotate(45deg);
    animation: photoShine 4s infinite;
  }

  @keyframes photoShine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  }
`;

// Information section
const InfoSection = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`;

const PersonalInfo = styled.div`
  flex: 1;
`;

const Name = styled.h2`
  color: #ffffff;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 4px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
`;

const Title = styled.h3`
  color: #00ffff;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 8px;
  opacity: 0.9;
`;

const Department = styled.div`
  color: #ffffff;
  font-size: 10px;
  opacity: 0.7;
  margin-bottom: 8px;
`;

const ContactDetails = styled.div`
  font-size: 9px;
  color: #ffffff;
  opacity: 0.8;
  line-height: 1.4;
`;

// Security features
const SecurityStrip = styled.div`
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 8px;
  background: linear-gradient(to bottom, 
    #ff0080 0%, 
    #00ffff 25%, 
    #80ff00 50%, 
    #ffff00 75%, 
    #ff0080 100%
  );
  border-radius: 0 12px 12px 0;
  opacity: 0.6;
`;

const HologramEffect = styled.div`
  position: absolute;
  bottom: 10px;
  right: 15px;
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, rgba(0, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: hologramPulse 2s ease-in-out infinite;

  @keyframes hologramPulse {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.1); }
  }
`;

export const IDCard: React.FC = () => {
  return (
    <IDCardContainer>
      <Lanyard>
        <LanyardText>PSTorque</LanyardText>
      </Lanyard>
      
      <Card
        initial={{ opacity: 0, y: 50, rotateX: -15 }}
        animate={{ opacity: 1, y: 0, rotateX: 0 }}
        transition={{ duration: 1, delay: 0.3 }}
        whileHover={{ 
          scale: 1.02,
          rotateY: 5,
          transition: { duration: 0.3 }
        }}
      >
        <SecurityStrip />
        
        <CardHeader>
          <CompanyLogo>PS</CompanyLogo>
          <IDNumber>ID: {new Date().getFullYear()}-{Math.floor(Math.random() * 1000).toString().padStart(3, '0')}</IDNumber>
        </CardHeader>
        
        <CardContent>
          <PhotoSection>
            <ProfilePhoto>👨‍💻</ProfilePhoto>
          </PhotoSection>
          
          <InfoSection>
            <PersonalInfo>
              <Name>{personalInfo.name}</Name>
              <Title>{personalInfo.title}</Title>
              <Department>Software Development</Department>
            </PersonalInfo>
            
            <ContactDetails>
              <div>📧 {contactInfo.email}</div>
              <div>📍 {personalInfo.location}</div>
              <div>💼 {personalInfo.yearsOfExperience}+ years exp.</div>
            </ContactDetails>
          </InfoSection>
        </CardContent>
        
        <HologramEffect />
      </Card>
    </IDCardContainer>
  );
};
