{"name": "terminal-portfolio", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@react-three/drei": "^10.5.1", "@react-three/fiber": "^9.2.0", "@react-three/rapier": "^2.1.0", "@types/styled-components": "^5.1.34", "framer-motion": "^10.16.16", "meshline": "^3.3.1", "react": "^18.2.0", "react-dom": "^18.2.0", "styled-components": "^6.1.8", "three": "^0.178.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8"}}