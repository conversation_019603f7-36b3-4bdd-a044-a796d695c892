{"version": 3, "sources": ["../../@react-three/rapier/dist/react-three-rapier.esm.js"], "sourcesContent": ["import { Vector3 as Vector3$1, Quaternion as Quaternion$1, ActiveEvents, ColliderDesc, EventQueue, RigidBodyDesc } from '@dimforge/rapier3d-compat';\nexport { CoefficientCombineRule, Collider as RapierCollider, RigidBody as RapierRigidBody } from '@dimforge/rapier3d-compat';\nimport { useFrame, useThree } from '@react-three/fiber';\nimport React, { useRef, useEffect, memo, useMemo, useContext, useState, createContext, useCallback, Fragment } from 'react';\nimport { Quaternion, Euler, Vector3, Object3D, Matrix4, BufferGeometry, BufferAttribute, MathUtils, DynamicDrawUsage } from 'three';\nimport { suspend } from 'suspend-react';\nimport { mergeVertices } from 'three-stdlib';\n\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\n\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\n\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\n\nconst _quaternion = new Quaternion();\nnew Euler();\nconst _vector3 = new Vector3();\nnew Object3D();\nconst _matrix4 = new Matrix4();\nconst _position = new Vector3();\nconst _rotation = new Quaternion();\nconst _scale = new Vector3();\n\nconst vectorArrayToVector3 = arr => {\n  const [x, y, z] = arr;\n  return new Vector3(x, y, z);\n};\nconst rapierQuaternionToQuaternion = ({\n  x,\n  y,\n  z,\n  w\n}) => _quaternion.set(x, y, z, w);\nconst vector3ToRapierVector = v => {\n  if (Array.isArray(v)) {\n    return new Vector3$1(v[0], v[1], v[2]);\n  } else if (typeof v === \"number\") {\n    return new Vector3$1(v, v, v);\n  } else {\n    const threeVector3 = v;\n    return new Vector3$1(threeVector3.x, threeVector3.y, threeVector3.z);\n  }\n};\nconst quaternionToRapierQuaternion = v => {\n  if (Array.isArray(v)) {\n    return new Quaternion$1(v[0], v[1], v[2], v[3]);\n  } else {\n    return new Quaternion$1(v.x, v.y, v.z, v.w);\n  }\n};\nconst rigidBodyTypeMap = {\n  fixed: 1,\n  dynamic: 0,\n  kinematicPosition: 2,\n  kinematicVelocity: 3\n};\nconst rigidBodyTypeFromString = type => rigidBodyTypeMap[type];\nconst scaleVertices = (vertices, scale) => {\n  const scaledVerts = Array.from(vertices);\n  for (let i = 0; i < vertices.length / 3; i++) {\n    scaledVerts[i * 3] *= scale.x;\n    scaledVerts[i * 3 + 1] *= scale.y;\n    scaledVerts[i * 3 + 2] *= scale.z;\n  }\n  return scaledVerts;\n};\nconst vectorToTuple = v => {\n  if (!v) return [0];\n  if (v instanceof Quaternion) {\n    return [v.x, v.y, v.z, v.w];\n  }\n  if (v instanceof Vector3 || v instanceof Euler) {\n    return [v.x, v.y, v.z];\n  }\n  if (Array.isArray(v)) {\n    return v;\n  }\n  return [v];\n};\nfunction useConst(initialValue) {\n  const ref = useRef(undefined);\n  if (ref.current === undefined) {\n    ref.current = {\n      value: typeof initialValue === \"function\" ? initialValue() : initialValue\n    };\n  }\n  return ref.current.value;\n}\n\nconst useRaf = callback => {\n  const cb = useRef(callback);\n  const raf = useRef(0);\n  const lastFrame = useRef(0);\n  useEffect(() => {\n    cb.current = callback;\n  }, [callback]);\n  useEffect(() => {\n    const loop = () => {\n      const now = performance.now();\n      const delta = now - lastFrame.current;\n      raf.current = requestAnimationFrame(loop);\n      cb.current(delta / 1000);\n      lastFrame.current = now;\n    };\n    raf.current = requestAnimationFrame(loop);\n    return () => cancelAnimationFrame(raf.current);\n  }, []);\n};\n\nconst UseFrameStepper = ({\n  onStep,\n  updatePriority\n}) => {\n  useFrame((_, dt) => {\n    onStep(dt);\n  }, updatePriority);\n  return null;\n};\nconst RafStepper = ({\n  onStep\n}) => {\n  useRaf(dt => {\n    onStep(dt);\n  });\n  return null;\n};\nconst FrameStepper = ({\n  onStep,\n  type,\n  updatePriority\n}) => {\n  return type === \"independent\" ? /*#__PURE__*/React.createElement(RafStepper, {\n    onStep: onStep\n  }) : /*#__PURE__*/React.createElement(UseFrameStepper, {\n    onStep: onStep,\n    updatePriority: updatePriority\n  });\n};\nvar FrameStepper$1 = /*#__PURE__*/memo(FrameStepper);\n\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\n\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\n\nconst _excluded$2 = [\"mass\", \"linearDamping\", \"angularDamping\", \"type\", \"onCollisionEnter\", \"onCollisionExit\", \"onIntersectionEnter\", \"onIntersectionExit\", \"onContactForce\", \"children\", \"canSleep\", \"ccd\", \"gravityScale\", \"softCcdPrediction\", \"ref\"];\nconst scaleColliderArgs = (shape, args, scale) => {\n  const newArgs = args.slice();\n\n  // Heightfield uses a vector\n  if (shape === \"heightfield\") {\n    const s = newArgs[3];\n    s.x *= scale.x;\n    s.x *= scale.y;\n    s.x *= scale.z;\n    return newArgs;\n  }\n\n  // Trimesh and convex scale the vertices\n  if (shape === \"trimesh\" || shape === \"convexHull\") {\n    newArgs[0] = scaleVertices(newArgs[0], scale);\n    return newArgs;\n  }\n\n  // Prepfill with some extra\n  const scaleArray = [scale.x, scale.y, scale.z, scale.x, scale.x];\n  return newArgs.map((arg, index) => scaleArray[index] * arg);\n};\nconst createColliderFromOptions = (options, world, scale, getRigidBody) => {\n  const scaledArgs = scaleColliderArgs(options.shape, options.args, scale);\n  // @ts-ignore\n  const desc = ColliderDesc[options.shape](...scaledArgs);\n  return world.createCollider(desc, getRigidBody === null || getRigidBody === void 0 ? void 0 : getRigidBody());\n};\nconst immutableColliderOptions = [\"shape\", \"args\"];\nconst massPropertiesConflictError = \"Please pick ONLY ONE of the `density`, `mass` and `massProperties` options.\";\nconst setColliderMassOptions = (collider, options) => {\n  if (options.density !== undefined) {\n    if (options.mass !== undefined || options.massProperties !== undefined) {\n      throw new Error(massPropertiesConflictError);\n    }\n    collider.setDensity(options.density);\n    return;\n  }\n  if (options.mass !== undefined) {\n    if (options.massProperties !== undefined) {\n      throw new Error(massPropertiesConflictError);\n    }\n    collider.setMass(options.mass);\n    return;\n  }\n  if (options.massProperties !== undefined) {\n    collider.setMassProperties(options.massProperties.mass, options.massProperties.centerOfMass, options.massProperties.principalAngularInertia, options.massProperties.angularInertiaLocalFrame);\n  }\n};\nconst mutableColliderOptions = {\n  sensor: (collider, value) => {\n    collider.setSensor(value);\n  },\n  collisionGroups: (collider, value) => {\n    collider.setCollisionGroups(value);\n  },\n  solverGroups: (collider, value) => {\n    collider.setSolverGroups(value);\n  },\n  friction: (collider, value) => {\n    collider.setFriction(value);\n  },\n  frictionCombineRule: (collider, value) => {\n    collider.setFrictionCombineRule(value);\n  },\n  restitution: (collider, value) => {\n    collider.setRestitution(value);\n  },\n  restitutionCombineRule: (collider, value) => {\n    collider.setRestitutionCombineRule(value);\n  },\n  activeCollisionTypes: (collider, value) => {\n    collider.setActiveCollisionTypes(value);\n  },\n  contactSkin: (collider, value) => {\n    collider.setContactSkin(value);\n  },\n  // To make sure the options all mutable options are listed\n  quaternion: () => {},\n  position: () => {},\n  rotation: () => {},\n  scale: () => {}\n};\nconst mutableColliderOptionKeys = Object.keys(mutableColliderOptions);\nconst setColliderOptions = (collider, options, states) => {\n  const state = states.get(collider.handle);\n  if (state) {\n    var _state$worldParent;\n    // Update collider position based on the object's position\n    const parentWorldScale = state.object.parent.getWorldScale(_vector3);\n    const parentInvertedWorldMatrix = (_state$worldParent = state.worldParent) === null || _state$worldParent === void 0 ? void 0 : _state$worldParent.matrixWorld.clone().invert();\n    state.object.updateWorldMatrix(true, false);\n    _matrix4.copy(state.object.matrixWorld);\n    if (parentInvertedWorldMatrix) {\n      _matrix4.premultiply(parentInvertedWorldMatrix);\n    }\n    _matrix4.decompose(_position, _rotation, _scale);\n    if (collider.parent()) {\n      collider.setTranslationWrtParent({\n        x: _position.x * parentWorldScale.x,\n        y: _position.y * parentWorldScale.y,\n        z: _position.z * parentWorldScale.z\n      });\n      collider.setRotationWrtParent(_rotation);\n    } else {\n      collider.setTranslation({\n        x: _position.x * parentWorldScale.x,\n        y: _position.y * parentWorldScale.y,\n        z: _position.z * parentWorldScale.z\n      });\n      collider.setRotation(_rotation);\n    }\n    mutableColliderOptionKeys.forEach(key => {\n      if (key in options) {\n        const option = options[key];\n        mutableColliderOptions[key](collider,\n        // @ts-ignore Option does not want to fit into the function, but it will\n        option, options);\n      }\n    });\n\n    // handle mass separately, because the assignments\n    // are exclusive.\n    setColliderMassOptions(collider, options);\n  }\n};\nconst useUpdateColliderOptions = (getCollider, props, states) => {\n  // TODO: Improve this, split each prop into its own effect\n  const mutablePropsAsFlatArray = useMemo(() => mutableColliderOptionKeys.flatMap(key => {\n    return vectorToTuple(props[key]);\n  }), [props]);\n  useEffect(() => {\n    const collider = getCollider();\n    setColliderOptions(collider, props, states);\n  }, [...mutablePropsAsFlatArray, getCollider]);\n};\nconst isChildOfMeshCollider = child => {\n  let flag = false;\n  child.traverseAncestors(a => {\n    if (a.userData.r3RapierType === \"MeshCollider\") flag = true;\n  });\n  return flag;\n};\nconst createColliderState = (collider, object, rigidBodyObject) => {\n  return {\n    collider,\n    worldParent: rigidBodyObject || undefined,\n    object\n  };\n};\nconst autoColliderMap = {\n  cuboid: \"cuboid\",\n  ball: \"ball\",\n  hull: \"convexHull\",\n  trimesh: \"trimesh\"\n};\nconst createColliderPropsFromChildren = ({\n  object,\n  ignoreMeshColliders: _ignoreMeshColliders = true,\n  options\n}) => {\n  const childColliderProps = [];\n  object.updateWorldMatrix(true, false);\n  const invertedParentMatrixWorld = object.matrixWorld.clone().invert();\n  const colliderFromChild = child => {\n    if (\"isMesh\" in child) {\n      if (_ignoreMeshColliders && isChildOfMeshCollider(child)) return;\n      const worldScale = child.getWorldScale(_scale);\n      const shape = autoColliderMap[options.colliders || \"cuboid\"];\n      child.updateWorldMatrix(true, false);\n      _matrix4.copy(child.matrixWorld).premultiply(invertedParentMatrixWorld).decompose(_position, _rotation, _scale);\n      const rotationEuler = new Euler().setFromQuaternion(_rotation, \"XYZ\");\n      const {\n        geometry\n      } = child;\n      const {\n        args,\n        offset\n      } = getColliderArgsFromGeometry(geometry, options.colliders || \"cuboid\");\n      const colliderProps = _objectSpread2(_objectSpread2({}, cleanRigidBodyPropsForCollider(options)), {}, {\n        args: args,\n        shape: shape,\n        rotation: [rotationEuler.x, rotationEuler.y, rotationEuler.z],\n        position: [_position.x + offset.x * worldScale.x, _position.y + offset.y * worldScale.y, _position.z + offset.z * worldScale.z],\n        scale: [worldScale.x, worldScale.y, worldScale.z]\n      });\n      childColliderProps.push(colliderProps);\n    }\n  };\n  if (options.includeInvisible) {\n    object.traverse(colliderFromChild);\n  } else {\n    object.traverseVisible(colliderFromChild);\n  }\n  return childColliderProps;\n};\nconst getColliderArgsFromGeometry = (geometry, colliders) => {\n  switch (colliders) {\n    case \"cuboid\":\n      {\n        geometry.computeBoundingBox();\n        const {\n          boundingBox\n        } = geometry;\n        const size = boundingBox.getSize(new Vector3());\n        return {\n          args: [size.x / 2, size.y / 2, size.z / 2],\n          offset: boundingBox.getCenter(new Vector3())\n        };\n      }\n    case \"ball\":\n      {\n        geometry.computeBoundingSphere();\n        const {\n          boundingSphere\n        } = geometry;\n        const radius = boundingSphere.radius;\n        return {\n          args: [radius],\n          offset: boundingSphere.center\n        };\n      }\n    case \"trimesh\":\n      {\n        var _clonedGeometry$index;\n        const clonedGeometry = geometry.index ? geometry.clone() : mergeVertices(geometry);\n        return {\n          args: [clonedGeometry.attributes.position.array, (_clonedGeometry$index = clonedGeometry.index) === null || _clonedGeometry$index === void 0 ? void 0 : _clonedGeometry$index.array],\n          offset: new Vector3()\n        };\n      }\n    case \"hull\":\n      {\n        const g = geometry.clone();\n        return {\n          args: [g.attributes.position.array],\n          offset: new Vector3()\n        };\n      }\n  }\n  return {\n    args: [],\n    offset: new Vector3()\n  };\n};\nconst getActiveCollisionEventsFromProps = props => {\n  return {\n    collision: !!(props !== null && props !== void 0 && props.onCollisionEnter || props !== null && props !== void 0 && props.onCollisionExit || props !== null && props !== void 0 && props.onIntersectionEnter || props !== null && props !== void 0 && props.onIntersectionExit),\n    contactForce: !!(props !== null && props !== void 0 && props.onContactForce)\n  };\n};\nconst useColliderEvents = (getCollider, props, events,\n/**\n * The RigidBody can pass down active events to the collider without attaching the event listners\n */\nactiveEvents = {}) => {\n  const {\n    onCollisionEnter,\n    onCollisionExit,\n    onIntersectionEnter,\n    onIntersectionExit,\n    onContactForce\n  } = props;\n  useEffect(() => {\n    const collider = getCollider();\n    if (collider) {\n      const {\n        collision: collisionEventsActive,\n        contactForce: contactForceEventsActive\n      } = getActiveCollisionEventsFromProps(props);\n      const hasCollisionEvent = collisionEventsActive || activeEvents.collision;\n      const hasContactForceEvent = contactForceEventsActive || activeEvents.contactForce;\n      if (hasCollisionEvent && hasContactForceEvent) {\n        collider.setActiveEvents(ActiveEvents.COLLISION_EVENTS | ActiveEvents.CONTACT_FORCE_EVENTS);\n      } else if (hasCollisionEvent) {\n        collider.setActiveEvents(ActiveEvents.COLLISION_EVENTS);\n      } else if (hasContactForceEvent) {\n        collider.setActiveEvents(ActiveEvents.CONTACT_FORCE_EVENTS);\n      }\n      events.set(collider.handle, {\n        onCollisionEnter,\n        onCollisionExit,\n        onIntersectionEnter,\n        onIntersectionExit,\n        onContactForce\n      });\n    }\n    return () => {\n      if (collider) {\n        events.delete(collider.handle);\n      }\n    };\n  }, [onCollisionEnter, onCollisionExit, onIntersectionEnter, onIntersectionExit, onContactForce, activeEvents]);\n};\nconst cleanRigidBodyPropsForCollider = (props = {}) => {\n  const rest = _objectWithoutProperties(props, _excluded$2);\n  return rest;\n};\n\n// Utils\nconst useMutableCallback = fn => {\n  const ref = useRef(fn);\n  useEffect(() => {\n    ref.current = fn;\n  }, [fn]);\n  return ref;\n};\n\n// External hooks\n/**\n * Exposes the Rapier context, and world\n * @category Hooks\n */\nconst useRapier = () => {\n  const rapier = useContext(rapierContext);\n  if (!rapier) throw new Error(\"react-three-rapier: useRapier must be used within <Physics />!\");\n  return rapier;\n};\n\n/**\n * Registers a callback to be called before the physics step\n * @category Hooks\n */\nconst useBeforePhysicsStep = callback => {\n  const {\n    beforeStepCallbacks\n  } = useRapier();\n  const ref = useMutableCallback(callback);\n  useEffect(() => {\n    beforeStepCallbacks.add(ref);\n    return () => {\n      beforeStepCallbacks.delete(ref);\n    };\n  }, []);\n};\n\n/**\n * Registers a callback to be called after the physics step\n * @category Hooks\n */\nconst useAfterPhysicsStep = callback => {\n  const {\n    afterStepCallbacks\n  } = useRapier();\n  const ref = useMutableCallback(callback);\n  useEffect(() => {\n    afterStepCallbacks.add(ref);\n    return () => {\n      afterStepCallbacks.delete(ref);\n    };\n  }, []);\n};\n\n// Internal hooks\n/**\n * @internal\n */\nconst useChildColliderProps = (ref, options, ignoreMeshColliders = true) => {\n  const [colliderProps, setColliderProps] = useState([]);\n  useEffect(() => {\n    const object = ref.current;\n    if (object && options.colliders !== false) {\n      setColliderProps(createColliderPropsFromChildren({\n        object: ref.current,\n        options,\n        ignoreMeshColliders\n      }));\n    }\n  }, [options.colliders]);\n  return colliderProps;\n};\n\nconst Debug = /*#__PURE__*/memo(() => {\n  const {\n    world\n  } = useRapier();\n  const ref = useRef(null);\n  useFrame(() => {\n    const mesh = ref.current;\n    if (!mesh) return;\n    const buffers = world.debugRender();\n    const geometry = new BufferGeometry();\n    geometry.setAttribute(\"position\", new BufferAttribute(buffers.vertices, 3));\n    geometry.setAttribute(\"color\", new BufferAttribute(buffers.colors, 4));\n    mesh.geometry.dispose();\n    mesh.geometry = geometry;\n  });\n  return /*#__PURE__*/React.createElement(\"group\", null, /*#__PURE__*/React.createElement(\"lineSegments\", {\n    ref: ref,\n    frustumCulled: false\n  }, /*#__PURE__*/React.createElement(\"lineBasicMaterial\", {\n    color: 0xffffff,\n    vertexColors: true\n  }), /*#__PURE__*/React.createElement(\"bufferGeometry\", null)));\n});\n\n/**\n * Creates a proxy that will create a singleton instance of the given class\n * when a property is accessed, and not before.\n *\n * @returns A proxy and a reset function, so that the instance can created again\n */\nconst createSingletonProxy = createInstance => {\n  let instance;\n  const handler = {\n    get(target, prop) {\n      if (!instance) {\n        instance = createInstance();\n      }\n      return Reflect.get(instance, prop);\n    },\n    set(target, prop, value) {\n      if (!instance) {\n        instance = createInstance();\n      }\n      return Reflect.set(instance, prop, value);\n    }\n  };\n  const proxy = new Proxy({}, handler);\n  const reset = () => {\n    instance = undefined;\n  };\n  const set = newInstance => {\n    instance = newInstance;\n  };\n\n  /**\n   * Return the proxy and a reset function\n   */\n  return {\n    proxy,\n    reset,\n    set\n  };\n};\n\nconst rapierContext = /*#__PURE__*/createContext(undefined);\nconst getCollisionPayloadFromSource = (target, other) => {\n  var _target$collider$stat, _target$rigidBody$sta, _other$collider$state, _other$rigidBody$stat, _other$collider$state2, _other$rigidBody$stat2;\n  return {\n    target: {\n      rigidBody: target.rigidBody.object,\n      collider: target.collider.object,\n      colliderObject: (_target$collider$stat = target.collider.state) === null || _target$collider$stat === void 0 ? void 0 : _target$collider$stat.object,\n      rigidBodyObject: (_target$rigidBody$sta = target.rigidBody.state) === null || _target$rigidBody$sta === void 0 ? void 0 : _target$rigidBody$sta.object\n    },\n    other: {\n      rigidBody: other.rigidBody.object,\n      collider: other.collider.object,\n      colliderObject: (_other$collider$state = other.collider.state) === null || _other$collider$state === void 0 ? void 0 : _other$collider$state.object,\n      rigidBodyObject: (_other$rigidBody$stat = other.rigidBody.state) === null || _other$rigidBody$stat === void 0 ? void 0 : _other$rigidBody$stat.object\n    },\n    rigidBody: other.rigidBody.object,\n    collider: other.collider.object,\n    colliderObject: (_other$collider$state2 = other.collider.state) === null || _other$collider$state2 === void 0 ? void 0 : _other$collider$state2.object,\n    rigidBodyObject: (_other$rigidBody$stat2 = other.rigidBody.state) === null || _other$rigidBody$stat2 === void 0 ? void 0 : _other$rigidBody$stat2.object\n  };\n};\nconst importRapier = async () => {\n  let r = await import('@dimforge/rapier3d-compat');\n  await r.init();\n  return r;\n};\n/**\n * The main physics component used to create a physics world.\n * @category Components\n */\nconst Physics = props => {\n  const {\n    colliders = \"cuboid\",\n    children,\n    timeStep = 1 / 60,\n    paused = false,\n    interpolate = true,\n    updatePriority,\n    updateLoop = \"follow\",\n    debug = false,\n    gravity = [0, -9.81, 0],\n    allowedLinearError = 0.001,\n    predictionDistance = 0.002,\n    numSolverIterations = 4,\n    numAdditionalFrictionIterations = 4,\n    numInternalPgsIterations = 1,\n    minIslandSize = 128,\n    maxCcdSubsteps = 1,\n    contactNaturalFrequency = 30,\n    lengthUnit = 1\n  } = props;\n  const rapier = suspend(importRapier, [\"@react-thee/rapier\", importRapier]);\n  const {\n    invalidate\n  } = useThree();\n  const rigidBodyStates = useConst(() => new Map());\n  const colliderStates = useConst(() => new Map());\n  const rigidBodyEvents = useConst(() => new Map());\n  const colliderEvents = useConst(() => new Map());\n  const eventQueue = useConst(() => new EventQueue(false));\n  const beforeStepCallbacks = useConst(() => new Set());\n  const afterStepCallbacks = useConst(() => new Set());\n\n  /**\n   * Initiate the world\n   * This creates a singleton proxy, so that the world is only created when\n   * something within it is accessed.\n   */\n  const {\n    proxy: worldProxy,\n    reset: resetWorldProxy,\n    set: setWorldProxy\n  } = useConst(() => createSingletonProxy(() => new rapier.World(vectorArrayToVector3(gravity))));\n  useEffect(() => {\n    return () => {\n      worldProxy.free();\n      resetWorldProxy();\n    };\n  }, []);\n\n  // Update mutable props\n  useEffect(() => {\n    worldProxy.gravity = vector3ToRapierVector(gravity);\n    worldProxy.integrationParameters.numSolverIterations = numSolverIterations;\n    worldProxy.integrationParameters.numAdditionalFrictionIterations = numAdditionalFrictionIterations;\n    worldProxy.integrationParameters.numInternalPgsIterations = numInternalPgsIterations;\n    worldProxy.integrationParameters.normalizedAllowedLinearError = allowedLinearError;\n    worldProxy.integrationParameters.minIslandSize = minIslandSize;\n    worldProxy.integrationParameters.maxCcdSubsteps = maxCcdSubsteps;\n    worldProxy.integrationParameters.normalizedPredictionDistance = predictionDistance;\n    worldProxy.lengthUnit = lengthUnit;\n    worldProxy.integrationParameters.contact_natural_frequency = contactNaturalFrequency;\n  }, [worldProxy, ...gravity, numSolverIterations, numAdditionalFrictionIterations, numInternalPgsIterations, allowedLinearError, minIslandSize, maxCcdSubsteps, predictionDistance, lengthUnit, contactNaturalFrequency]);\n  const getSourceFromColliderHandle = useCallback(handle => {\n    var _collider$parent;\n    const collider = worldProxy.getCollider(handle);\n    const colEvents = colliderEvents.get(handle);\n    const colliderState = colliderStates.get(handle);\n    const rigidBodyHandle = collider === null || collider === void 0 ? void 0 : (_collider$parent = collider.parent()) === null || _collider$parent === void 0 ? void 0 : _collider$parent.handle;\n    const rigidBody = rigidBodyHandle !== undefined ? worldProxy.getRigidBody(rigidBodyHandle) : undefined;\n    const rbEvents = rigidBody && rigidBodyHandle !== undefined ? rigidBodyEvents.get(rigidBodyHandle) : undefined;\n    const rigidBodyState = rigidBodyHandle !== undefined ? rigidBodyStates.get(rigidBodyHandle) : undefined;\n    const source = {\n      collider: {\n        object: collider,\n        events: colEvents,\n        state: colliderState\n      },\n      rigidBody: {\n        object: rigidBody,\n        events: rbEvents,\n        state: rigidBodyState\n      }\n    };\n    return source;\n  }, []);\n  const [steppingState] = useState({\n    previousState: {},\n    accumulator: 0\n  });\n  const step = useCallback(dt => {\n    const world = worldProxy;\n\n    /* Check if the timestep is supposed to be variable. We'll do this here\n      once so we don't have to string-check every frame. */\n    const timeStepVariable = timeStep === \"vary\";\n\n    /**\n     * Fixed timeStep simulation progression\n     * @see https://gafferongames.com/post/fix_your_timestep/\n     */\n\n    const clampedDelta = MathUtils.clamp(dt, 0, 0.5);\n    const stepWorld = delta => {\n      // Trigger beforeStep callbacks\n      beforeStepCallbacks.forEach(callback => {\n        callback.current(world);\n      });\n      world.timestep = delta;\n      world.step(eventQueue);\n\n      // Trigger afterStep callbacks\n      afterStepCallbacks.forEach(callback => {\n        callback.current(world);\n      });\n    };\n    if (timeStepVariable) {\n      stepWorld(clampedDelta);\n    } else {\n      // don't step time forwards if paused\n      // Increase accumulator\n      steppingState.accumulator += clampedDelta;\n      while (steppingState.accumulator >= timeStep) {\n        // Set up previous state\n        // needed for accurate interpolations if the world steps more than once\n        if (interpolate) {\n          steppingState.previousState = {};\n          world.forEachRigidBody(body => {\n            steppingState.previousState[body.handle] = {\n              position: body.translation(),\n              rotation: body.rotation()\n            };\n          });\n        }\n        stepWorld(timeStep);\n        steppingState.accumulator -= timeStep;\n      }\n    }\n    const interpolationAlpha = timeStepVariable || !interpolate || paused ? 1 : steppingState.accumulator / timeStep;\n\n    // Update meshes\n    rigidBodyStates.forEach((state, handle) => {\n      const rigidBody = world.getRigidBody(handle);\n      const events = rigidBodyEvents.get(handle);\n      if (events !== null && events !== void 0 && events.onSleep || events !== null && events !== void 0 && events.onWake) {\n        if (rigidBody.isSleeping() && !state.isSleeping) {\n          var _events$onSleep;\n          events === null || events === void 0 ? void 0 : (_events$onSleep = events.onSleep) === null || _events$onSleep === void 0 ? void 0 : _events$onSleep.call(events);\n        }\n        if (!rigidBody.isSleeping() && state.isSleeping) {\n          var _events$onWake;\n          events === null || events === void 0 ? void 0 : (_events$onWake = events.onWake) === null || _events$onWake === void 0 ? void 0 : _events$onWake.call(events);\n        }\n        state.isSleeping = rigidBody.isSleeping();\n      }\n      if (!rigidBody || rigidBody.isSleeping() && !(\"isInstancedMesh\" in state.object) || !state.setMatrix) {\n        return;\n      }\n\n      // New states\n      let t = rigidBody.translation();\n      let r = rigidBody.rotation();\n      let previousState = steppingState.previousState[handle];\n      if (previousState) {\n        // Get previous simulated world position\n        _matrix4.compose(previousState.position, rapierQuaternionToQuaternion(previousState.rotation), state.scale).premultiply(state.invertedWorldMatrix).decompose(_position, _rotation, _scale);\n\n        // Apply previous tick position\n        if (state.meshType == \"mesh\") {\n          state.object.position.copy(_position);\n          state.object.quaternion.copy(_rotation);\n        }\n      }\n\n      // Get new position\n      _matrix4.compose(t, rapierQuaternionToQuaternion(r), state.scale).premultiply(state.invertedWorldMatrix).decompose(_position, _rotation, _scale);\n      if (state.meshType == \"instancedMesh\") {\n        state.setMatrix(_matrix4);\n      } else {\n        // Interpolate to new position\n        state.object.position.lerp(_position, interpolationAlpha);\n        state.object.quaternion.slerp(_rotation, interpolationAlpha);\n      }\n    });\n    eventQueue.drainCollisionEvents((handle1, handle2, started) => {\n      const source1 = getSourceFromColliderHandle(handle1);\n      const source2 = getSourceFromColliderHandle(handle2);\n\n      // Collision Events\n      if (!(source1 !== null && source1 !== void 0 && source1.collider.object) || !(source2 !== null && source2 !== void 0 && source2.collider.object)) {\n        return;\n      }\n      const collisionPayload1 = getCollisionPayloadFromSource(source1, source2);\n      const collisionPayload2 = getCollisionPayloadFromSource(source2, source1);\n      if (started) {\n        world.contactPair(source1.collider.object, source2.collider.object, (manifold, flipped) => {\n          var _source1$rigidBody$ev, _source1$rigidBody$ev2, _source2$rigidBody$ev, _source2$rigidBody$ev2, _source1$collider$eve, _source1$collider$eve2, _source2$collider$eve, _source2$collider$eve2;\n          /* RigidBody events */\n          (_source1$rigidBody$ev = source1.rigidBody.events) === null || _source1$rigidBody$ev === void 0 ? void 0 : (_source1$rigidBody$ev2 = _source1$rigidBody$ev.onCollisionEnter) === null || _source1$rigidBody$ev2 === void 0 ? void 0 : _source1$rigidBody$ev2.call(_source1$rigidBody$ev, _objectSpread2(_objectSpread2({}, collisionPayload1), {}, {\n            manifold,\n            flipped\n          }));\n          (_source2$rigidBody$ev = source2.rigidBody.events) === null || _source2$rigidBody$ev === void 0 ? void 0 : (_source2$rigidBody$ev2 = _source2$rigidBody$ev.onCollisionEnter) === null || _source2$rigidBody$ev2 === void 0 ? void 0 : _source2$rigidBody$ev2.call(_source2$rigidBody$ev, _objectSpread2(_objectSpread2({}, collisionPayload2), {}, {\n            manifold,\n            flipped\n          }));\n\n          /* Collider events */\n          (_source1$collider$eve = source1.collider.events) === null || _source1$collider$eve === void 0 ? void 0 : (_source1$collider$eve2 = _source1$collider$eve.onCollisionEnter) === null || _source1$collider$eve2 === void 0 ? void 0 : _source1$collider$eve2.call(_source1$collider$eve, _objectSpread2(_objectSpread2({}, collisionPayload1), {}, {\n            manifold,\n            flipped\n          }));\n          (_source2$collider$eve = source2.collider.events) === null || _source2$collider$eve === void 0 ? void 0 : (_source2$collider$eve2 = _source2$collider$eve.onCollisionEnter) === null || _source2$collider$eve2 === void 0 ? void 0 : _source2$collider$eve2.call(_source2$collider$eve, _objectSpread2(_objectSpread2({}, collisionPayload2), {}, {\n            manifold,\n            flipped\n          }));\n        });\n      } else {\n        var _source1$rigidBody$ev3, _source1$rigidBody$ev4, _source2$rigidBody$ev3, _source2$rigidBody$ev4, _source1$collider$eve3, _source1$collider$eve4, _source2$collider$eve3, _source2$collider$eve4;\n        (_source1$rigidBody$ev3 = source1.rigidBody.events) === null || _source1$rigidBody$ev3 === void 0 ? void 0 : (_source1$rigidBody$ev4 = _source1$rigidBody$ev3.onCollisionExit) === null || _source1$rigidBody$ev4 === void 0 ? void 0 : _source1$rigidBody$ev4.call(_source1$rigidBody$ev3, collisionPayload1);\n        (_source2$rigidBody$ev3 = source2.rigidBody.events) === null || _source2$rigidBody$ev3 === void 0 ? void 0 : (_source2$rigidBody$ev4 = _source2$rigidBody$ev3.onCollisionExit) === null || _source2$rigidBody$ev4 === void 0 ? void 0 : _source2$rigidBody$ev4.call(_source2$rigidBody$ev3, collisionPayload2);\n        (_source1$collider$eve3 = source1.collider.events) === null || _source1$collider$eve3 === void 0 ? void 0 : (_source1$collider$eve4 = _source1$collider$eve3.onCollisionExit) === null || _source1$collider$eve4 === void 0 ? void 0 : _source1$collider$eve4.call(_source1$collider$eve3, collisionPayload1);\n        (_source2$collider$eve3 = source2.collider.events) === null || _source2$collider$eve3 === void 0 ? void 0 : (_source2$collider$eve4 = _source2$collider$eve3.onCollisionExit) === null || _source2$collider$eve4 === void 0 ? void 0 : _source2$collider$eve4.call(_source2$collider$eve3, collisionPayload2);\n      }\n\n      // Sensor Intersections\n      if (started) {\n        if (world.intersectionPair(source1.collider.object, source2.collider.object)) {\n          var _source1$rigidBody$ev5, _source1$rigidBody$ev6, _source2$rigidBody$ev5, _source2$rigidBody$ev6, _source1$collider$eve5, _source1$collider$eve6, _source2$collider$eve5, _source2$collider$eve6;\n          (_source1$rigidBody$ev5 = source1.rigidBody.events) === null || _source1$rigidBody$ev5 === void 0 ? void 0 : (_source1$rigidBody$ev6 = _source1$rigidBody$ev5.onIntersectionEnter) === null || _source1$rigidBody$ev6 === void 0 ? void 0 : _source1$rigidBody$ev6.call(_source1$rigidBody$ev5, collisionPayload1);\n          (_source2$rigidBody$ev5 = source2.rigidBody.events) === null || _source2$rigidBody$ev5 === void 0 ? void 0 : (_source2$rigidBody$ev6 = _source2$rigidBody$ev5.onIntersectionEnter) === null || _source2$rigidBody$ev6 === void 0 ? void 0 : _source2$rigidBody$ev6.call(_source2$rigidBody$ev5, collisionPayload2);\n          (_source1$collider$eve5 = source1.collider.events) === null || _source1$collider$eve5 === void 0 ? void 0 : (_source1$collider$eve6 = _source1$collider$eve5.onIntersectionEnter) === null || _source1$collider$eve6 === void 0 ? void 0 : _source1$collider$eve6.call(_source1$collider$eve5, collisionPayload1);\n          (_source2$collider$eve5 = source2.collider.events) === null || _source2$collider$eve5 === void 0 ? void 0 : (_source2$collider$eve6 = _source2$collider$eve5.onIntersectionEnter) === null || _source2$collider$eve6 === void 0 ? void 0 : _source2$collider$eve6.call(_source2$collider$eve5, collisionPayload2);\n        }\n      } else {\n        var _source1$rigidBody$ev7, _source1$rigidBody$ev8, _source2$rigidBody$ev7, _source2$rigidBody$ev8, _source1$collider$eve7, _source1$collider$eve8, _source2$collider$eve7, _source2$collider$eve8;\n        (_source1$rigidBody$ev7 = source1.rigidBody.events) === null || _source1$rigidBody$ev7 === void 0 ? void 0 : (_source1$rigidBody$ev8 = _source1$rigidBody$ev7.onIntersectionExit) === null || _source1$rigidBody$ev8 === void 0 ? void 0 : _source1$rigidBody$ev8.call(_source1$rigidBody$ev7, collisionPayload1);\n        (_source2$rigidBody$ev7 = source2.rigidBody.events) === null || _source2$rigidBody$ev7 === void 0 ? void 0 : (_source2$rigidBody$ev8 = _source2$rigidBody$ev7.onIntersectionExit) === null || _source2$rigidBody$ev8 === void 0 ? void 0 : _source2$rigidBody$ev8.call(_source2$rigidBody$ev7, collisionPayload2);\n        (_source1$collider$eve7 = source1.collider.events) === null || _source1$collider$eve7 === void 0 ? void 0 : (_source1$collider$eve8 = _source1$collider$eve7.onIntersectionExit) === null || _source1$collider$eve8 === void 0 ? void 0 : _source1$collider$eve8.call(_source1$collider$eve7, collisionPayload1);\n        (_source2$collider$eve7 = source2.collider.events) === null || _source2$collider$eve7 === void 0 ? void 0 : (_source2$collider$eve8 = _source2$collider$eve7.onIntersectionExit) === null || _source2$collider$eve8 === void 0 ? void 0 : _source2$collider$eve8.call(_source2$collider$eve7, collisionPayload2);\n      }\n    });\n    eventQueue.drainContactForceEvents(event => {\n      var _source1$rigidBody$ev9, _source1$rigidBody$ev10, _source2$rigidBody$ev9, _source2$rigidBody$ev10, _source1$collider$eve9, _source1$collider$eve10, _source2$collider$eve9, _source2$collider$eve10;\n      const source1 = getSourceFromColliderHandle(event.collider1());\n      const source2 = getSourceFromColliderHandle(event.collider2());\n\n      // Collision Events\n      if (!(source1 !== null && source1 !== void 0 && source1.collider.object) || !(source2 !== null && source2 !== void 0 && source2.collider.object)) {\n        return;\n      }\n      const collisionPayload1 = getCollisionPayloadFromSource(source1, source2);\n      const collisionPayload2 = getCollisionPayloadFromSource(source2, source1);\n      (_source1$rigidBody$ev9 = source1.rigidBody.events) === null || _source1$rigidBody$ev9 === void 0 ? void 0 : (_source1$rigidBody$ev10 = _source1$rigidBody$ev9.onContactForce) === null || _source1$rigidBody$ev10 === void 0 ? void 0 : _source1$rigidBody$ev10.call(_source1$rigidBody$ev9, _objectSpread2(_objectSpread2({}, collisionPayload1), {}, {\n        totalForce: event.totalForce(),\n        totalForceMagnitude: event.totalForceMagnitude(),\n        maxForceDirection: event.maxForceDirection(),\n        maxForceMagnitude: event.maxForceMagnitude()\n      }));\n      (_source2$rigidBody$ev9 = source2.rigidBody.events) === null || _source2$rigidBody$ev9 === void 0 ? void 0 : (_source2$rigidBody$ev10 = _source2$rigidBody$ev9.onContactForce) === null || _source2$rigidBody$ev10 === void 0 ? void 0 : _source2$rigidBody$ev10.call(_source2$rigidBody$ev9, _objectSpread2(_objectSpread2({}, collisionPayload2), {}, {\n        totalForce: event.totalForce(),\n        totalForceMagnitude: event.totalForceMagnitude(),\n        maxForceDirection: event.maxForceDirection(),\n        maxForceMagnitude: event.maxForceMagnitude()\n      }));\n      (_source1$collider$eve9 = source1.collider.events) === null || _source1$collider$eve9 === void 0 ? void 0 : (_source1$collider$eve10 = _source1$collider$eve9.onContactForce) === null || _source1$collider$eve10 === void 0 ? void 0 : _source1$collider$eve10.call(_source1$collider$eve9, _objectSpread2(_objectSpread2({}, collisionPayload1), {}, {\n        totalForce: event.totalForce(),\n        totalForceMagnitude: event.totalForceMagnitude(),\n        maxForceDirection: event.maxForceDirection(),\n        maxForceMagnitude: event.maxForceMagnitude()\n      }));\n      (_source2$collider$eve9 = source2.collider.events) === null || _source2$collider$eve9 === void 0 ? void 0 : (_source2$collider$eve10 = _source2$collider$eve9.onContactForce) === null || _source2$collider$eve10 === void 0 ? void 0 : _source2$collider$eve10.call(_source2$collider$eve9, _objectSpread2(_objectSpread2({}, collisionPayload2), {}, {\n        totalForce: event.totalForce(),\n        totalForceMagnitude: event.totalForceMagnitude(),\n        maxForceDirection: event.maxForceDirection(),\n        maxForceMagnitude: event.maxForceMagnitude()\n      }));\n    });\n    world.forEachActiveRigidBody(() => {\n      invalidate();\n    });\n  }, [paused, timeStep, interpolate, worldProxy]);\n  const context = useMemo(() => ({\n    rapier,\n    world: worldProxy,\n    setWorld: world => {\n      setWorldProxy(world);\n    },\n    physicsOptions: {\n      colliders,\n      gravity\n    },\n    rigidBodyStates,\n    colliderStates,\n    rigidBodyEvents,\n    colliderEvents,\n    beforeStepCallbacks,\n    afterStepCallbacks,\n    isPaused: paused,\n    isDebug: debug,\n    step\n  }), [paused, step, debug, colliders, gravity]);\n  const stepCallback = useCallback(delta => {\n    if (!paused) {\n      step(delta);\n    }\n  }, [paused, step]);\n  return /*#__PURE__*/React.createElement(rapierContext.Provider, {\n    value: context\n  }, /*#__PURE__*/React.createElement(FrameStepper$1, {\n    onStep: stepCallback,\n    type: updateLoop,\n    updatePriority: updatePriority\n  }), debug && /*#__PURE__*/React.createElement(Debug, null), children);\n};\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\n// Need to catch the case where forwardedRef is a function... how to do that?\nconst useForwardedRef = (forwardedRef, defaultValue = null) => {\n  const innerRef = useRef(defaultValue);\n\n  // Update the forwarded ref when the inner ref changes\n  if (forwardedRef && typeof forwardedRef !== \"function\") {\n    if (!forwardedRef.current) {\n      forwardedRef.current = innerRef.current;\n    }\n    return forwardedRef;\n  }\n  return innerRef;\n};\n\n/**\n * Initiate an instance and return a safe getter\n */\nconst useImperativeInstance = (createFn, destroyFn, dependencyList) => {\n  const ref = useRef(undefined);\n  const getInstance = useCallback(() => {\n    if (!ref.current) {\n      ref.current = createFn();\n    }\n    return ref.current;\n  }, dependencyList);\n  useEffect(() => {\n    // Save the destroy function and instance\n    const instance = getInstance();\n    const destroy = () => destroyFn(instance);\n    return () => {\n      destroy();\n      ref.current = undefined;\n    };\n  }, [getInstance]);\n  return getInstance;\n};\n\nconst rigidBodyDescFromOptions = options => {\n  var _options$canSleep;\n  const type = rigidBodyTypeFromString((options === null || options === void 0 ? void 0 : options.type) || \"dynamic\");\n  const desc = new RigidBodyDesc(type);\n\n  // Apply immutable options\n  desc.canSleep = (_options$canSleep = options === null || options === void 0 ? void 0 : options.canSleep) !== null && _options$canSleep !== void 0 ? _options$canSleep : true;\n  return desc;\n};\nconst createRigidBodyState = ({\n  rigidBody,\n  object,\n  setMatrix,\n  getMatrix,\n  worldScale,\n  meshType: _meshType = \"mesh\"\n}) => {\n  object.updateWorldMatrix(true, false);\n  const invertedWorldMatrix = object.parent.matrixWorld.clone().invert();\n  return {\n    object,\n    rigidBody,\n    invertedWorldMatrix,\n    setMatrix: setMatrix ? setMatrix : matrix => {\n      object.matrix.copy(matrix);\n    },\n    getMatrix: getMatrix ? getMatrix : matrix => matrix.copy(object.matrix),\n    scale: worldScale || object.getWorldScale(_scale).clone(),\n    isSleeping: false,\n    meshType: _meshType\n  };\n};\nconst immutableRigidBodyOptions = [\"args\", \"colliders\", \"canSleep\"];\nconst mutableRigidBodyOptions = {\n  gravityScale: (rb, value) => {\n    rb.setGravityScale(value, true);\n  },\n  additionalSolverIterations(rb, value) {\n    rb.setAdditionalSolverIterations(value);\n  },\n  linearDamping: (rb, value) => {\n    rb.setLinearDamping(value);\n  },\n  angularDamping: (rb, value) => {\n    rb.setAngularDamping(value);\n  },\n  dominanceGroup: (rb, value) => {\n    rb.setDominanceGroup(value);\n  },\n  enabledRotations: (rb, [x, y, z]) => {\n    rb.setEnabledRotations(x, y, z, true);\n  },\n  enabledTranslations: (rb, [x, y, z]) => {\n    rb.setEnabledTranslations(x, y, z, true);\n  },\n  lockRotations: (rb, value) => {\n    rb.lockRotations(value, true);\n  },\n  lockTranslations: (rb, value) => {\n    rb.lockTranslations(value, true);\n  },\n  angularVelocity: (rb, [x, y, z]) => {\n    rb.setAngvel({\n      x,\n      y,\n      z\n    }, true);\n  },\n  linearVelocity: (rb, [x, y, z]) => {\n    rb.setLinvel({\n      x,\n      y,\n      z\n    }, true);\n  },\n  ccd: (rb, value) => {\n    rb.enableCcd(value);\n  },\n  softCcdPrediction: (rb, value) => {\n    rb.setSoftCcdPrediction(value);\n  },\n  userData: (rb, value) => {\n    rb.userData = value;\n  },\n  type(rb, value) {\n    rb.setBodyType(rigidBodyTypeFromString(value), true);\n  },\n  position: () => {},\n  rotation: () => {},\n  quaternion: () => {},\n  scale: () => {}\n};\nconst mutableRigidBodyOptionKeys = Object.keys(mutableRigidBodyOptions);\nconst setRigidBodyOptions = (rigidBody, options, states, updateTranslations = true) => {\n  if (!rigidBody) {\n    return;\n  }\n  const state = states.get(rigidBody.handle);\n  if (state) {\n    if (updateTranslations) {\n      state.object.updateWorldMatrix(true, false);\n      _matrix4.copy(state.object.matrixWorld).decompose(_position, _rotation, _scale);\n      rigidBody.setTranslation(_position, false);\n      rigidBody.setRotation(_rotation, false);\n    }\n    mutableRigidBodyOptionKeys.forEach(key => {\n      if (key in options) {\n        mutableRigidBodyOptions[key](rigidBody, options[key]);\n      }\n    });\n  }\n};\nconst useUpdateRigidBodyOptions = (getRigidBody, props, states, updateTranslations = true) => {\n  // TODO: Improve this, split each prop into its own effect\n  const mutablePropsAsFlatArray = useMemo(() => mutableRigidBodyOptionKeys.flatMap(key => {\n    return vectorToTuple(props[key]);\n  }), [props]);\n  useEffect(() => {\n    const rigidBody = getRigidBody();\n    setRigidBodyOptions(rigidBody, props, states, updateTranslations);\n  }, mutablePropsAsFlatArray);\n};\nconst useRigidBodyEvents = (getRigidBody, props, events) => {\n  const {\n    onWake,\n    onSleep,\n    onCollisionEnter,\n    onCollisionExit,\n    onIntersectionEnter,\n    onIntersectionExit,\n    onContactForce\n  } = props;\n  const eventHandlers = {\n    onWake,\n    onSleep,\n    onCollisionEnter,\n    onCollisionExit,\n    onIntersectionEnter,\n    onIntersectionExit,\n    onContactForce\n  };\n  useEffect(() => {\n    const rigidBody = getRigidBody();\n    events.set(rigidBody.handle, eventHandlers);\n    return () => {\n      events.delete(rigidBody.handle);\n    };\n  }, [onWake, onSleep, onCollisionEnter, onCollisionExit, onIntersectionEnter, onIntersectionExit, onContactForce]);\n};\n\n/**\n * Takes an object resembling a Vector3 and returs a Three.Vector3\n * @category Math helpers\n */\nconst vec3 = ({\n  x,\n  y,\n  z\n} = {\n  x: 0,\n  y: 0,\n  z: 0\n}) => {\n  return new Vector3(x, y, z);\n};\n\n/**\n * Takes an object resembling a Quaternion and returs a Three.Quaternion\n * @category Math helpers\n */\nconst quat = ({\n  x,\n  y,\n  z,\n  w\n} = {\n  x: 0,\n  y: 0,\n  z: 0,\n  w: 1\n}) => {\n  return new Quaternion(x, y, z, w);\n};\n\n/**\n * Takes an object resembling an Euler and returs a Three.Euler\n * @category Math helpers\n */\nconst euler = ({\n  x,\n  y,\n  z\n} = {\n  x: 0,\n  y: 0,\n  z: 0\n}) => {\n  return new Euler(x, y, z);\n};\n\n/**\n * A collider is a shape that can be attached to a rigid body to define its physical properties.\n * @internal\n */\nconst AnyCollider = /*#__PURE__*/memo(props => {\n  const {\n    children,\n    position,\n    rotation,\n    quaternion,\n    scale,\n    name\n  } = props;\n  const {\n    world,\n    colliderEvents,\n    colliderStates\n  } = useRapier();\n  const rigidBodyContext = useRigidBodyContext();\n  const colliderRef = useForwardedRef(props.ref);\n  const objectRef = useRef(null);\n\n  // We spread the props out here to make sure that the ref is updated when the props change.\n  const immutablePropArray = immutableColliderOptions.flatMap(key =>\n  // Array.isArray(props[key]) ? [...props[key]] : props[key]\n  Array.isArray(props[key]) ? props[key] : [props[key]]);\n  const getInstance = useImperativeInstance(() => {\n    const worldScale = objectRef.current.getWorldScale(vec3());\n    const collider = createColliderFromOptions(props, world, worldScale, rigidBodyContext === null || rigidBodyContext === void 0 ? void 0 : rigidBodyContext.getRigidBody);\n    if (typeof props.ref == \"function\") {\n      props.ref(collider);\n    }\n    colliderRef.current = collider;\n    return collider;\n  }, collider => {\n    if (world.getCollider(collider.handle)) {\n      world.removeCollider(collider, true);\n    }\n  }, [...immutablePropArray, rigidBodyContext]);\n  useEffect(() => {\n    const collider = getInstance();\n    colliderStates.set(collider.handle, createColliderState(collider, objectRef.current, rigidBodyContext === null || rigidBodyContext === void 0 ? void 0 : rigidBodyContext.ref.current));\n    return () => {\n      colliderStates.delete(collider.handle);\n    };\n  }, [getInstance]);\n  const mergedProps = useMemo(() => {\n    return _objectSpread2(_objectSpread2({}, cleanRigidBodyPropsForCollider(rigidBodyContext === null || rigidBodyContext === void 0 ? void 0 : rigidBodyContext.options)), props);\n  }, [props, rigidBodyContext === null || rigidBodyContext === void 0 ? void 0 : rigidBodyContext.options]);\n  useUpdateColliderOptions(getInstance, mergedProps, colliderStates);\n  useColliderEvents(getInstance, mergedProps, colliderEvents, getActiveCollisionEventsFromProps(rigidBodyContext === null || rigidBodyContext === void 0 ? void 0 : rigidBodyContext.options));\n  return /*#__PURE__*/React.createElement(\"object3D\", {\n    position: position,\n    rotation: rotation,\n    quaternion: quaternion,\n    scale: scale,\n    ref: objectRef,\n    name: name\n  }, children);\n});\n/**\n * A cuboid collider shape\n * @category Colliders\n */\nconst CuboidCollider = /*#__PURE__*/React.forwardRef((props, ref) => {\n  return /*#__PURE__*/React.createElement(AnyCollider, _extends({}, props, {\n    shape: \"cuboid\",\n    ref: ref\n  }));\n});\nCuboidCollider.displayName = \"CuboidCollider\";\n/**\n * A round cuboid collider shape\n * @category Colliders\n */\nconst RoundCuboidCollider = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(AnyCollider, _extends({}, props, {\n  shape: \"roundCuboid\",\n  ref: ref\n})));\nRoundCuboidCollider.displayName = \"RoundCuboidCollider\";\n/**\n * A ball collider shape\n * @category Colliders\n */\nconst BallCollider = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(AnyCollider, _extends({}, props, {\n  shape: \"ball\",\n  ref: ref\n})));\nBallCollider.displayName = \"BallCollider\";\n/**\n * A capsule collider shape\n * @category Colliders\n */\nconst CapsuleCollider = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(AnyCollider, _extends({}, props, {\n  shape: \"capsule\",\n  ref: ref\n})));\nCapsuleCollider.displayName = \"CapsuleCollider\";\n/**\n * A heightfield collider shape\n * @category Colliders\n */\nconst HeightfieldCollider = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(AnyCollider, _extends({}, props, {\n  shape: \"heightfield\",\n  ref: ref\n})));\nHeightfieldCollider.displayName = \"HeightfieldCollider\";\n/**\n * A trimesh collider shape\n * @category Colliders\n */\nconst TrimeshCollider = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(AnyCollider, _extends({}, props, {\n  shape: \"trimesh\",\n  ref: ref\n})));\nTrimeshCollider.displayName = \"TrimeshCollider\";\n/**\n * A cone collider shape\n * @category Colliders\n */\nconst ConeCollider = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(AnyCollider, _extends({}, props, {\n  shape: \"cone\",\n  ref: ref\n})));\nConeCollider.displayName = \"ConeCollider\";\n/**\n * A round cylinder collider shape\n * @category Colliders\n */\nconst RoundConeCollider = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(AnyCollider, _extends({}, props, {\n  shape: \"roundCone\",\n  ref: ref\n})));\nRoundConeCollider.displayName = \"RoundConeCollider\";\n/**\n * A cylinder collider shape\n * @category Colliders\n */\nconst CylinderCollider = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(AnyCollider, _extends({}, props, {\n  shape: \"cylinder\",\n  ref: ref\n})));\nCylinderCollider.displayName = \"CylinderCollider\";\n/**\n * A round cylinder collider shape\n * @category Colliders\n */\nconst RoundCylinderCollider = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(AnyCollider, _extends({}, props, {\n  shape: \"roundCylinder\",\n  ref: ref\n})));\nCylinderCollider.displayName = \"RoundCylinderCollider\";\n/**\n * A convex hull collider shape\n * @category Colliders\n */\nconst ConvexHullCollider = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(AnyCollider, _extends({}, props, {\n  shape: \"convexHull\",\n  ref: ref\n})));\nConvexHullCollider.displayName = \"ConvexHullCollider\";\n\nconst _excluded$1 = [\"ref\", \"children\", \"type\", \"position\", \"rotation\", \"scale\", \"quaternion\", \"transformState\"];\nconst RigidBodyContext = /*#__PURE__*/createContext(undefined);\nconst useRigidBodyContext = () => useContext(RigidBodyContext);\n/**\n * A rigid body is a physical object that can be simulated by the physics engine.\n * @category Components\n */\nconst RigidBody = /*#__PURE__*/memo(props => {\n  const {\n      ref,\n      children,\n      type,\n      position,\n      rotation,\n      scale,\n      quaternion,\n      transformState\n    } = props,\n    objectProps = _objectWithoutProperties(props, _excluded$1);\n  const objectRef = useRef(null);\n  const rigidBodyRef = useForwardedRef(ref);\n  const {\n    world,\n    rigidBodyStates,\n    physicsOptions,\n    rigidBodyEvents\n  } = useRapier();\n  const mergedOptions = useMemo(() => {\n    return _objectSpread2(_objectSpread2(_objectSpread2({}, physicsOptions), props), {}, {\n      children: undefined\n    });\n  }, [physicsOptions, props]);\n  const immutablePropArray = immutableRigidBodyOptions.flatMap(key => {\n    return Array.isArray(mergedOptions[key]) ? [...mergedOptions[key]] : mergedOptions[key];\n  });\n  const childColliderProps = useChildColliderProps(objectRef, mergedOptions);\n\n  // Provide a way to eagerly create rigidbody\n  const getRigidBody = useImperativeInstance(() => {\n    const desc = rigidBodyDescFromOptions(mergedOptions);\n    const rigidBody = world.createRigidBody(desc);\n    if (typeof ref === \"function\") {\n      ref(rigidBody);\n    }\n    rigidBodyRef.current = rigidBody;\n    return rigidBody;\n  }, rigidBody => {\n    if (world.getRigidBody(rigidBody.handle)) {\n      world.removeRigidBody(rigidBody);\n    }\n  }, immutablePropArray);\n\n  // Only provide a object state after the ref has been set\n  useEffect(() => {\n    const rigidBody = getRigidBody();\n    const state = createRigidBodyState({\n      rigidBody,\n      object: objectRef.current\n    });\n    rigidBodyStates.set(rigidBody.handle, props.transformState ? props.transformState(state) : state);\n    return () => {\n      rigidBodyStates.delete(rigidBody.handle);\n    };\n  }, [getRigidBody]);\n  useUpdateRigidBodyOptions(getRigidBody, mergedOptions, rigidBodyStates);\n  useRigidBodyEvents(getRigidBody, mergedOptions, rigidBodyEvents);\n  const contextValue = useMemo(() => {\n    return {\n      ref: objectRef,\n      getRigidBody: getRigidBody,\n      options: mergedOptions\n    };\n  }, [getRigidBody]);\n  return /*#__PURE__*/React.createElement(RigidBodyContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(\"object3D\", _extends({\n    ref: objectRef\n  }, objectProps, {\n    position: position,\n    rotation: rotation,\n    quaternion: quaternion,\n    scale: scale\n  }), children, childColliderProps.map((colliderProps, index) => /*#__PURE__*/React.createElement(AnyCollider, _extends({\n    key: index\n  }, colliderProps)))));\n});\nRigidBody.displayName = \"RigidBody\";\n\n/**\n * A mesh collider is a collider that is automatically generated from the geometry of the children.\n * @category Colliders\n */\nconst MeshCollider = /*#__PURE__*/memo(props => {\n  const {\n    children,\n    type\n  } = props;\n  const {\n    physicsOptions\n  } = useRapier();\n  const object = useRef(null);\n  const {\n    options\n  } = useRigidBodyContext();\n  const mergedOptions = useMemo(() => {\n    return _objectSpread2(_objectSpread2(_objectSpread2({}, physicsOptions), options), {}, {\n      children: undefined,\n      colliders: type\n    });\n  }, [physicsOptions, options]);\n  const childColliderProps = useChildColliderProps(object, mergedOptions, false);\n  return /*#__PURE__*/React.createElement(\"object3D\", {\n    ref: object,\n    userData: {\n      r3RapierType: \"MeshCollider\"\n    }\n  }, children, childColliderProps.map((colliderProps, index) => /*#__PURE__*/React.createElement(AnyCollider, _extends({\n    key: index\n  }, colliderProps))));\n});\nMeshCollider.displayName = \"MeshCollider\";\n\nconst _excluded = [\"ref\"],\n  _excluded2 = [\"children\", \"instances\", \"colliderNodes\", \"position\", \"rotation\", \"quaternion\", \"scale\"];\nconst InstancedRigidBodies = /*#__PURE__*/memo(_ref => {\n  let {\n      ref\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  const rigidBodiesRef = useForwardedRef(ref, []);\n  const objectRef = useRef(null);\n  const instanceWrapperRef = useRef(null);\n  const {\n      // instanced props\n      children,\n      instances,\n      colliderNodes = [],\n      // wrapper object props\n      position,\n      rotation,\n      quaternion,\n      scale\n\n      // rigid body specific props, and r3f-object props\n    } = props,\n    rigidBodyProps = _objectWithoutProperties(props, _excluded2);\n  const childColliderProps = useChildColliderProps(objectRef, _objectSpread2(_objectSpread2({}, props), {}, {\n    children: undefined\n  }));\n  const getInstancedMesh = () => {\n    const firstChild = instanceWrapperRef.current.children[0];\n    if (firstChild && \"isInstancedMesh\" in firstChild) {\n      return firstChild;\n    }\n    return undefined;\n  };\n  useEffect(() => {\n    const instancedMesh = getInstancedMesh();\n    if (instancedMesh) {\n      instancedMesh.instanceMatrix.setUsage(DynamicDrawUsage);\n    } else {\n      console.warn(\"InstancedRigidBodies expects exactly one child, which must be an InstancedMesh\");\n    }\n  }, []);\n\n  // Update the RigidBodyStates whenever the instances change\n  const applyInstancedState = (state, index) => {\n    const instancedMesh = getInstancedMesh();\n    if (instancedMesh) {\n      return _objectSpread2(_objectSpread2({}, state), {}, {\n        getMatrix: matrix => {\n          instancedMesh.getMatrixAt(index, matrix);\n          return matrix;\n        },\n        setMatrix: matrix => {\n          instancedMesh.setMatrixAt(index, matrix);\n          instancedMesh.instanceMatrix.needsUpdate = true;\n        },\n        meshType: \"instancedMesh\"\n      });\n    }\n    return state;\n  };\n  return /*#__PURE__*/React.createElement(\"object3D\", _extends({\n    ref: objectRef\n  }, rigidBodyProps, {\n    position: position,\n    rotation: rotation,\n    quaternion: quaternion,\n    scale: scale\n  }), /*#__PURE__*/React.createElement(\"object3D\", {\n    ref: instanceWrapperRef\n  }, children), instances === null || instances === void 0 ? void 0 : instances.map((instance, index) => /*#__PURE__*/React.createElement(RigidBody, _extends({}, rigidBodyProps, instance, {\n    ref: body => {\n      rigidBodiesRef.current[index] = body;\n    },\n    transformState: state => applyInstancedState(state, index)\n  }), /*#__PURE__*/React.createElement(React.Fragment, null, colliderNodes.map((node, index) => /*#__PURE__*/React.createElement(Fragment, {\n    key: index\n  }, node)), childColliderProps.map((colliderProps, colliderIndex) => /*#__PURE__*/React.createElement(AnyCollider, _extends({\n    key: colliderIndex\n  }, colliderProps)))))));\n});\nInstancedRigidBodies.displayName = \"InstancedRigidBodies\";\n\n/**\n * @internal\n */\nconst useImpulseJoint = (body1, body2, params) => {\n  const {\n    world\n  } = useRapier();\n  const jointRef = useRef(undefined);\n  useImperativeInstance(() => {\n    if (body1.current && body2.current) {\n      const newJoint = world.createImpulseJoint(params, body1.current, body2.current, true);\n      jointRef.current = newJoint;\n      return newJoint;\n    }\n  }, joint => {\n    if (joint) {\n      jointRef.current = undefined;\n      if (world.getImpulseJoint(joint.handle)) {\n        world.removeImpulseJoint(joint, true);\n      }\n    }\n  }, []);\n  return jointRef;\n};\n\n/**\n * A fixed joint ensures that two rigid-bodies don't move relative to each other.\n * Fixed joints are characterized by one local frame (represented by an isometry) on each rigid-body.\n * The fixed-joint makes these frames coincide in world-space.\n *\n * @category Hooks - Joints\n */\nconst useFixedJoint = (body1, body2, [body1Anchor, body1LocalFrame, body2Anchor, body2LocalFrame]) => {\n  const {\n    rapier\n  } = useRapier();\n  return useImpulseJoint(body1, body2, rapier.JointData.fixed(vector3ToRapierVector(body1Anchor), quaternionToRapierQuaternion(body1LocalFrame), vector3ToRapierVector(body2Anchor), quaternionToRapierQuaternion(body2LocalFrame)));\n};\n\n/**\n * The spherical joint ensures that two points on the local-spaces of two rigid-bodies always coincide (it prevents any relative\n * translational motion at this points). This is typically used to simulate ragdolls arms, pendulums, etc.\n * They are characterized by one local anchor on each rigid-body. Each anchor represents the location of the\n * points that need to coincide on the local-space of each rigid-body.\n *\n * @category Hooks - Joints\n */\nconst useSphericalJoint = (body1, body2, [body1Anchor, body2Anchor]) => {\n  const {\n    rapier\n  } = useRapier();\n  return useImpulseJoint(body1, body2, rapier.JointData.spherical(vector3ToRapierVector(body1Anchor), vector3ToRapierVector(body2Anchor)));\n};\n\n/**\n * The revolute joint prevents any relative movement between two rigid-bodies, except for relative\n * rotations along one axis. This is typically used to simulate wheels, fans, etc.\n * They are characterized by one local anchor as well as one local axis on each rigid-body.\n *\n * @category Hooks - Joints\n */\nconst useRevoluteJoint = (body1, body2, [body1Anchor, body2Anchor, axis, limits]) => {\n  const {\n    rapier\n  } = useRapier();\n  const params = rapier.JointData.revolute(vector3ToRapierVector(body1Anchor), vector3ToRapierVector(body2Anchor), vector3ToRapierVector(axis));\n  if (limits) {\n    params.limitsEnabled = true;\n    params.limits = limits;\n  }\n  return useImpulseJoint(body1, body2, params);\n};\n\n/**\n * The prismatic joint prevents any relative movement between two rigid-bodies, except for relative translations along one axis.\n * It is characterized by one local anchor as well as one local axis on each rigid-body. In 3D, an optional\n * local tangent axis can be specified for each rigid-body.\n *\n * @category Hooks - Joints\n */\nconst usePrismaticJoint = (body1, body2, [body1Anchor, body2Anchor, axis, limits]) => {\n  const {\n    rapier\n  } = useRapier();\n  const params = rapier.JointData.prismatic(vector3ToRapierVector(body1Anchor), vector3ToRapierVector(body2Anchor), vector3ToRapierVector(axis));\n  if (limits) {\n    params.limitsEnabled = true;\n    params.limits = limits;\n  }\n  return useImpulseJoint(body1, body2, params);\n};\n\n/**\n * The rope joint limits the max distance between two bodies.\n * @category Hooks - Joints\n */\nconst useRopeJoint = (body1, body2, [body1Anchor, body2Anchor, length]) => {\n  const {\n    rapier\n  } = useRapier();\n  const vBody1Anchor = vector3ToRapierVector(body1Anchor);\n  const vBody2Anchor = vector3ToRapierVector(body2Anchor);\n  const params = rapier.JointData.rope(length, vBody1Anchor, vBody2Anchor);\n  return useImpulseJoint(body1, body2, params);\n};\n\n/**\n * The spring joint applies a force proportional to the distance between two objects.\n * @category Hooks - Joints\n */\nconst useSpringJoint = (body1, body2, [body1Anchor, body2Anchor, restLength, stiffness, damping]) => {\n  const {\n    rapier\n  } = useRapier();\n  const vBody1Anchor = vector3ToRapierVector(body1Anchor);\n  const vBody2Anchor = vector3ToRapierVector(body2Anchor);\n  const params = rapier.JointData.spring(restLength, stiffness, damping, vBody1Anchor, vBody2Anchor);\n  return useImpulseJoint(body1, body2, params);\n};\n\n/**\n * Calculates an InteractionGroup bitmask for use in the `collisionGroups` or `solverGroups`\n * properties of RigidBody or Collider components. The first argument represents a list of\n * groups the entity is in (expressed as numbers from 0 to 15). The second argument is a list\n * of groups that will be filtered against. When it is omitted, all groups are filtered against.\n *\n * @example\n * A RigidBody that is member of group 0 and will collide with everything from groups 0 and 1:\n *\n * ```tsx\n * <RigidBody collisionGroups={interactionGroups([0], [0, 1])} />\n * ```\n *\n * A RigidBody that is member of groups 0 and 1 and will collide with everything else:\n *\n * ```tsx\n * <RigidBody collisionGroups={interactionGroups([0, 1])} />\n * ```\n *\n * A RigidBody that is member of groups 0 and 1 and will not collide with anything:\n *\n * ```tsx\n * <RigidBody collisionGroups={interactionGroups([0, 1], [])} />\n * ```\n *\n * Please note that Rapier needs interaction filters to evaluate to true between _both_ colliding\n * entities for collision events to trigger.\n *\n * @param memberships Groups the collider is a member of. (Values can range from 0 to 15.)\n * @param filters Groups the interaction group should filter against. (Values can range from 0 to 15.)\n * @returns An InteractionGroup bitmask.\n */\nconst interactionGroups = (memberships, filters) => (bitmask(memberships) << 16) + (filters !== undefined ? bitmask(filters) : 0b1111111111111111);\nconst bitmask = groups => [groups].flat().reduce((acc, layer) => acc | 1 << layer, 0);\n\nexport { AnyCollider, BallCollider, CapsuleCollider, ConeCollider, ConvexHullCollider, CuboidCollider, CylinderCollider, HeightfieldCollider, InstancedRigidBodies, MeshCollider, Physics, RigidBody, RoundConeCollider, RoundCuboidCollider, RoundCylinderCollider, TrimeshCollider, euler, interactionGroups, quat, useAfterPhysicsStep, useBeforePhysicsStep, useFixedJoint, useImpulseJoint, usePrismaticJoint, useRapier, useRevoluteJoint, useRopeJoint, useSphericalJoint, useSpringJoint, vec3 };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,mBAAoH;AAKpH,SAAS,aAAa,GAAG,GAAG;AAC1B,MAAI,YAAY,OAAO,KAAK,CAAC,EAAG,QAAO;AACvC,MAAI,IAAI,EAAE,OAAO,WAAW;AAC5B,MAAI,WAAW,GAAG;AAChB,QAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAChC,QAAI,YAAY,OAAO,EAAG,QAAO;AACjC,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAC7C;AAEA,SAAS,eAAe,GAAG;AACzB,MAAI,IAAI,aAAa,GAAG,QAAQ;AAChC,SAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AACxC;AAEA,SAAS,gBAAgB,GAAG,GAAG,GAAG;AAChC,UAAQ,IAAI,eAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG;AAAA,IAChE,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AACjB;AAEA,SAAS,QAAQ,GAAG,GAAG;AACrB,MAAI,IAAI,OAAO,KAAK,CAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAI,IAAI,OAAO,sBAAsB,CAAC;AACtC,UAAM,IAAI,EAAE,OAAO,SAAUA,IAAG;AAC9B,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAC/C,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,eAAe,GAAG;AACzB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,IAAI,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAC/C,QAAI,IAAI,QAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUA,IAAG;AAClD,sBAAgB,GAAGA,IAAG,EAAEA,EAAC,CAAC;AAAA,IAC5B,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUA,IAAG;AAChJ,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,IAAM,cAAc,IAAI,WAAW;AACnC,IAAI,MAAM;AACV,IAAM,WAAW,IAAI,QAAQ;AAC7B,IAAI,SAAS;AACb,IAAM,WAAW,IAAI,QAAQ;AAC7B,IAAM,YAAY,IAAI,QAAQ;AAC9B,IAAM,YAAY,IAAI,WAAW;AACjC,IAAM,SAAS,IAAI,QAAQ;AAE3B,IAAM,uBAAuB,SAAO;AAClC,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI;AAClB,SAAO,IAAI,QAAQ,GAAG,GAAG,CAAC;AAC5B;AACA,IAAM,+BAA+B,CAAC;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM,YAAY,IAAI,GAAG,GAAG,GAAG,CAAC;AAChC,IAAM,wBAAwB,OAAK;AACjC,MAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,WAAO,IAAI,GAAU,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACvC,WAAW,OAAO,MAAM,UAAU;AAChC,WAAO,IAAI,GAAU,GAAG,GAAG,CAAC;AAAA,EAC9B,OAAO;AACL,UAAM,eAAe;AACrB,WAAO,IAAI,GAAU,aAAa,GAAG,aAAa,GAAG,aAAa,CAAC;AAAA,EACrE;AACF;AACA,IAAM,+BAA+B,OAAK;AACxC,MAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,WAAO,IAAI,GAAa,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EAChD,OAAO;AACL,WAAO,IAAI,GAAa,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAAA,EAC5C;AACF;AACA,IAAM,mBAAmB;AAAA,EACvB,OAAO;AAAA,EACP,SAAS;AAAA,EACT,mBAAmB;AAAA,EACnB,mBAAmB;AACrB;AACA,IAAM,0BAA0B,UAAQ,iBAAiB,IAAI;AAC7D,IAAM,gBAAgB,CAAC,UAAU,UAAU;AACzC,QAAM,cAAc,MAAM,KAAK,QAAQ;AACvC,WAAS,IAAI,GAAG,IAAI,SAAS,SAAS,GAAG,KAAK;AAC5C,gBAAY,IAAI,CAAC,KAAK,MAAM;AAC5B,gBAAY,IAAI,IAAI,CAAC,KAAK,MAAM;AAChC,gBAAY,IAAI,IAAI,CAAC,KAAK,MAAM;AAAA,EAClC;AACA,SAAO;AACT;AACA,IAAM,gBAAgB,OAAK;AACzB,MAAI,CAAC,EAAG,QAAO,CAAC,CAAC;AACjB,MAAI,aAAa,YAAY;AAC3B,WAAO,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAAA,EAC5B;AACA,MAAI,aAAa,WAAW,aAAa,OAAO;AAC9C,WAAO,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAAA,EACvB;AACA,MAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,WAAO;AAAA,EACT;AACA,SAAO,CAAC,CAAC;AACX;AACA,SAAS,SAAS,cAAc;AAC9B,QAAM,UAAM,qBAAO,MAAS;AAC5B,MAAI,IAAI,YAAY,QAAW;AAC7B,QAAI,UAAU;AAAA,MACZ,OAAO,OAAO,iBAAiB,aAAa,aAAa,IAAI;AAAA,IAC/D;AAAA,EACF;AACA,SAAO,IAAI,QAAQ;AACrB;AAEA,IAAM,SAAS,cAAY;AACzB,QAAM,SAAK,qBAAO,QAAQ;AAC1B,QAAM,UAAM,qBAAO,CAAC;AACpB,QAAM,gBAAY,qBAAO,CAAC;AAC1B,8BAAU,MAAM;AACd,OAAG,UAAU;AAAA,EACf,GAAG,CAAC,QAAQ,CAAC;AACb,8BAAU,MAAM;AACd,UAAM,OAAO,MAAM;AACjB,YAAM,MAAM,YAAY,IAAI;AAC5B,YAAM,QAAQ,MAAM,UAAU;AAC9B,UAAI,UAAU,sBAAsB,IAAI;AACxC,SAAG,QAAQ,QAAQ,GAAI;AACvB,gBAAU,UAAU;AAAA,IACtB;AACA,QAAI,UAAU,sBAAsB,IAAI;AACxC,WAAO,MAAM,qBAAqB,IAAI,OAAO;AAAA,EAC/C,GAAG,CAAC,CAAC;AACP;AAEA,IAAM,kBAAkB,CAAC;AAAA,EACvB;AAAA,EACA;AACF,MAAM;AACJ,WAAS,CAAC,GAAG,OAAO;AAClB,WAAO,EAAE;AAAA,EACX,GAAG,cAAc;AACjB,SAAO;AACT;AACA,IAAM,aAAa,CAAC;AAAA,EAClB;AACF,MAAM;AACJ,SAAO,QAAM;AACX,WAAO,EAAE;AAAA,EACX,CAAC;AACD,SAAO;AACT;AACA,IAAM,eAAe,CAAC;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,SAAO,SAAS,gBAA6B,aAAAC,QAAM,cAAc,YAAY;AAAA,IAC3E;AAAA,EACF,CAAC,IAAiB,aAAAA,QAAM,cAAc,iBAAiB;AAAA,IACrD;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,IAAI,qBAA8B,mBAAK,YAAY;AAEnD,SAAS,8BAA8B,GAAG,GAAG;AAC3C,MAAI,QAAQ,EAAG,QAAO,CAAC;AACvB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,CAAC,EAAE,eAAe,KAAK,GAAG,CAAC,GAAG;AACjD,QAAI,OAAO,EAAE,QAAQ,CAAC,EAAG;AACzB,MAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EACZ;AACA,SAAO;AACT;AAEA,SAAS,yBAAyB,GAAG,GAAG;AACtC,MAAI,QAAQ,EAAG,QAAO,CAAC;AACvB,MAAI,GACF,GACA,IAAI,8BAA8B,GAAG,CAAC;AACxC,MAAI,OAAO,uBAAuB;AAChC,QAAI,IAAI,OAAO,sBAAsB,CAAC;AACtC,SAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,KAAI,EAAE,CAAC,GAAG,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,qBAAqB,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EACnH;AACA,SAAO;AACT;AAEA,IAAM,cAAc,CAAC,QAAQ,iBAAiB,kBAAkB,QAAQ,oBAAoB,mBAAmB,uBAAuB,sBAAsB,kBAAkB,YAAY,YAAY,OAAO,gBAAgB,qBAAqB,KAAK;AACvP,IAAM,oBAAoB,CAAC,OAAO,MAAM,UAAU;AAChD,QAAM,UAAU,KAAK,MAAM;AAG3B,MAAI,UAAU,eAAe;AAC3B,UAAM,IAAI,QAAQ,CAAC;AACnB,MAAE,KAAK,MAAM;AACb,MAAE,KAAK,MAAM;AACb,MAAE,KAAK,MAAM;AACb,WAAO;AAAA,EACT;AAGA,MAAI,UAAU,aAAa,UAAU,cAAc;AACjD,YAAQ,CAAC,IAAI,cAAc,QAAQ,CAAC,GAAG,KAAK;AAC5C,WAAO;AAAA,EACT;AAGA,QAAM,aAAa,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAC/D,SAAO,QAAQ,IAAI,CAAC,KAAK,UAAU,WAAW,KAAK,IAAI,GAAG;AAC5D;AACA,IAAM,4BAA4B,CAAC,SAAS,OAAO,OAAO,iBAAiB;AACzE,QAAM,aAAa,kBAAkB,QAAQ,OAAO,QAAQ,MAAM,KAAK;AAEvE,QAAM,OAAO,GAAa,QAAQ,KAAK,EAAE,GAAG,UAAU;AACtD,SAAO,MAAM,eAAe,MAAM,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,CAAC;AAC9G;AACA,IAAM,2BAA2B,CAAC,SAAS,MAAM;AACjD,IAAM,8BAA8B;AACpC,IAAM,yBAAyB,CAAC,UAAU,YAAY;AACpD,MAAI,QAAQ,YAAY,QAAW;AACjC,QAAI,QAAQ,SAAS,UAAa,QAAQ,mBAAmB,QAAW;AACtE,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AACA,aAAS,WAAW,QAAQ,OAAO;AACnC;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,QAAW;AAC9B,QAAI,QAAQ,mBAAmB,QAAW;AACxC,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AACA,aAAS,QAAQ,QAAQ,IAAI;AAC7B;AAAA,EACF;AACA,MAAI,QAAQ,mBAAmB,QAAW;AACxC,aAAS,kBAAkB,QAAQ,eAAe,MAAM,QAAQ,eAAe,cAAc,QAAQ,eAAe,yBAAyB,QAAQ,eAAe,wBAAwB;AAAA,EAC9L;AACF;AACA,IAAM,yBAAyB;AAAA,EAC7B,QAAQ,CAAC,UAAU,UAAU;AAC3B,aAAS,UAAU,KAAK;AAAA,EAC1B;AAAA,EACA,iBAAiB,CAAC,UAAU,UAAU;AACpC,aAAS,mBAAmB,KAAK;AAAA,EACnC;AAAA,EACA,cAAc,CAAC,UAAU,UAAU;AACjC,aAAS,gBAAgB,KAAK;AAAA,EAChC;AAAA,EACA,UAAU,CAAC,UAAU,UAAU;AAC7B,aAAS,YAAY,KAAK;AAAA,EAC5B;AAAA,EACA,qBAAqB,CAAC,UAAU,UAAU;AACxC,aAAS,uBAAuB,KAAK;AAAA,EACvC;AAAA,EACA,aAAa,CAAC,UAAU,UAAU;AAChC,aAAS,eAAe,KAAK;AAAA,EAC/B;AAAA,EACA,wBAAwB,CAAC,UAAU,UAAU;AAC3C,aAAS,0BAA0B,KAAK;AAAA,EAC1C;AAAA,EACA,sBAAsB,CAAC,UAAU,UAAU;AACzC,aAAS,wBAAwB,KAAK;AAAA,EACxC;AAAA,EACA,aAAa,CAAC,UAAU,UAAU;AAChC,aAAS,eAAe,KAAK;AAAA,EAC/B;AAAA;AAAA,EAEA,YAAY,MAAM;AAAA,EAAC;AAAA,EACnB,UAAU,MAAM;AAAA,EAAC;AAAA,EACjB,UAAU,MAAM;AAAA,EAAC;AAAA,EACjB,OAAO,MAAM;AAAA,EAAC;AAChB;AACA,IAAM,4BAA4B,OAAO,KAAK,sBAAsB;AACpE,IAAM,qBAAqB,CAAC,UAAU,SAAS,WAAW;AACxD,QAAM,QAAQ,OAAO,IAAI,SAAS,MAAM;AACxC,MAAI,OAAO;AACT,QAAI;AAEJ,UAAM,mBAAmB,MAAM,OAAO,OAAO,cAAc,QAAQ;AACnE,UAAM,6BAA6B,qBAAqB,MAAM,iBAAiB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,YAAY,MAAM,EAAE,OAAO;AAC9K,UAAM,OAAO,kBAAkB,MAAM,KAAK;AAC1C,aAAS,KAAK,MAAM,OAAO,WAAW;AACtC,QAAI,2BAA2B;AAC7B,eAAS,YAAY,yBAAyB;AAAA,IAChD;AACA,aAAS,UAAU,WAAW,WAAW,MAAM;AAC/C,QAAI,SAAS,OAAO,GAAG;AACrB,eAAS,wBAAwB;AAAA,QAC/B,GAAG,UAAU,IAAI,iBAAiB;AAAA,QAClC,GAAG,UAAU,IAAI,iBAAiB;AAAA,QAClC,GAAG,UAAU,IAAI,iBAAiB;AAAA,MACpC,CAAC;AACD,eAAS,qBAAqB,SAAS;AAAA,IACzC,OAAO;AACL,eAAS,eAAe;AAAA,QACtB,GAAG,UAAU,IAAI,iBAAiB;AAAA,QAClC,GAAG,UAAU,IAAI,iBAAiB;AAAA,QAClC,GAAG,UAAU,IAAI,iBAAiB;AAAA,MACpC,CAAC;AACD,eAAS,YAAY,SAAS;AAAA,IAChC;AACA,8BAA0B,QAAQ,SAAO;AACvC,UAAI,OAAO,SAAS;AAClB,cAAM,SAAS,QAAQ,GAAG;AAC1B,+BAAuB,GAAG;AAAA,UAAE;AAAA;AAAA,UAE5B;AAAA,UAAQ;AAAA,QAAO;AAAA,MACjB;AAAA,IACF,CAAC;AAID,2BAAuB,UAAU,OAAO;AAAA,EAC1C;AACF;AACA,IAAM,2BAA2B,CAAC,aAAa,OAAO,WAAW;AAE/D,QAAM,8BAA0B,sBAAQ,MAAM,0BAA0B,QAAQ,SAAO;AACrF,WAAO,cAAc,MAAM,GAAG,CAAC;AAAA,EACjC,CAAC,GAAG,CAAC,KAAK,CAAC;AACX,8BAAU,MAAM;AACd,UAAM,WAAW,YAAY;AAC7B,uBAAmB,UAAU,OAAO,MAAM;AAAA,EAC5C,GAAG,CAAC,GAAG,yBAAyB,WAAW,CAAC;AAC9C;AACA,IAAM,wBAAwB,WAAS;AACrC,MAAI,OAAO;AACX,QAAM,kBAAkB,OAAK;AAC3B,QAAI,EAAE,SAAS,iBAAiB,eAAgB,QAAO;AAAA,EACzD,CAAC;AACD,SAAO;AACT;AACA,IAAM,sBAAsB,CAAC,UAAU,QAAQ,oBAAoB;AACjE,SAAO;AAAA,IACL;AAAA,IACA,aAAa,mBAAmB;AAAA,IAChC;AAAA,EACF;AACF;AACA,IAAM,kBAAkB;AAAA,EACtB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,kCAAkC,CAAC;AAAA,EACvC;AAAA,EACA,qBAAqB,uBAAuB;AAAA,EAC5C;AACF,MAAM;AACJ,QAAM,qBAAqB,CAAC;AAC5B,SAAO,kBAAkB,MAAM,KAAK;AACpC,QAAM,4BAA4B,OAAO,YAAY,MAAM,EAAE,OAAO;AACpE,QAAM,oBAAoB,WAAS;AACjC,QAAI,YAAY,OAAO;AACrB,UAAI,wBAAwB,sBAAsB,KAAK,EAAG;AAC1D,YAAM,aAAa,MAAM,cAAc,MAAM;AAC7C,YAAM,QAAQ,gBAAgB,QAAQ,aAAa,QAAQ;AAC3D,YAAM,kBAAkB,MAAM,KAAK;AACnC,eAAS,KAAK,MAAM,WAAW,EAAE,YAAY,yBAAyB,EAAE,UAAU,WAAW,WAAW,MAAM;AAC9G,YAAM,gBAAgB,IAAI,MAAM,EAAE,kBAAkB,WAAW,KAAK;AACpE,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,4BAA4B,UAAU,QAAQ,aAAa,QAAQ;AACvE,YAAM,gBAAgB,eAAe,eAAe,CAAC,GAAG,+BAA+B,OAAO,CAAC,GAAG,CAAC,GAAG;AAAA,QACpG;AAAA,QACA;AAAA,QACA,UAAU,CAAC,cAAc,GAAG,cAAc,GAAG,cAAc,CAAC;AAAA,QAC5D,UAAU,CAAC,UAAU,IAAI,OAAO,IAAI,WAAW,GAAG,UAAU,IAAI,OAAO,IAAI,WAAW,GAAG,UAAU,IAAI,OAAO,IAAI,WAAW,CAAC;AAAA,QAC9H,OAAO,CAAC,WAAW,GAAG,WAAW,GAAG,WAAW,CAAC;AAAA,MAClD,CAAC;AACD,yBAAmB,KAAK,aAAa;AAAA,IACvC;AAAA,EACF;AACA,MAAI,QAAQ,kBAAkB;AAC5B,WAAO,SAAS,iBAAiB;AAAA,EACnC,OAAO;AACL,WAAO,gBAAgB,iBAAiB;AAAA,EAC1C;AACA,SAAO;AACT;AACA,IAAM,8BAA8B,CAAC,UAAU,cAAc;AAC3D,UAAQ,WAAW;AAAA,IACjB,KAAK,UACH;AACE,eAAS,mBAAmB;AAC5B,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM,OAAO,YAAY,QAAQ,IAAI,QAAQ,CAAC;AAC9C,aAAO;AAAA,QACL,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC;AAAA,QACzC,QAAQ,YAAY,UAAU,IAAI,QAAQ,CAAC;AAAA,MAC7C;AAAA,IACF;AAAA,IACF,KAAK,QACH;AACE,eAAS,sBAAsB;AAC/B,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM,SAAS,eAAe;AAC9B,aAAO;AAAA,QACL,MAAM,CAAC,MAAM;AAAA,QACb,QAAQ,eAAe;AAAA,MACzB;AAAA,IACF;AAAA,IACF,KAAK,WACH;AACE,UAAI;AACJ,YAAM,iBAAiB,SAAS,QAAQ,SAAS,MAAM,IAAI,cAAc,QAAQ;AACjF,aAAO;AAAA,QACL,MAAM,CAAC,eAAe,WAAW,SAAS,QAAQ,wBAAwB,eAAe,WAAW,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,KAAK;AAAA,QACnL,QAAQ,IAAI,QAAQ;AAAA,MACtB;AAAA,IACF;AAAA,IACF,KAAK,QACH;AACE,YAAM,IAAI,SAAS,MAAM;AACzB,aAAO;AAAA,QACL,MAAM,CAAC,EAAE,WAAW,SAAS,KAAK;AAAA,QAClC,QAAQ,IAAI,QAAQ;AAAA,MACtB;AAAA,IACF;AAAA,EACJ;AACA,SAAO;AAAA,IACL,MAAM,CAAC;AAAA,IACP,QAAQ,IAAI,QAAQ;AAAA,EACtB;AACF;AACA,IAAM,oCAAoC,WAAS;AACjD,SAAO;AAAA,IACL,WAAW,CAAC,EAAE,UAAU,QAAQ,UAAU,UAAU,MAAM,oBAAoB,UAAU,QAAQ,UAAU,UAAU,MAAM,mBAAmB,UAAU,QAAQ,UAAU,UAAU,MAAM,uBAAuB,UAAU,QAAQ,UAAU,UAAU,MAAM;AAAA,IAC5P,cAAc,CAAC,EAAE,UAAU,QAAQ,UAAU,UAAU,MAAM;AAAA,EAC/D;AACF;AACA,IAAM,oBAAoB,CAAC,aAAa,OAAO,QAI/C,eAAe,CAAC,MAAM;AACpB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,8BAAU,MAAM;AACd,UAAM,WAAW,YAAY;AAC7B,QAAI,UAAU;AACZ,YAAM;AAAA,QACJ,WAAW;AAAA,QACX,cAAc;AAAA,MAChB,IAAI,kCAAkC,KAAK;AAC3C,YAAM,oBAAoB,yBAAyB,aAAa;AAChE,YAAM,uBAAuB,4BAA4B,aAAa;AACtE,UAAI,qBAAqB,sBAAsB;AAC7C,iBAAS,gBAAgB,GAAa,mBAAmB,GAAa,oBAAoB;AAAA,MAC5F,WAAW,mBAAmB;AAC5B,iBAAS,gBAAgB,GAAa,gBAAgB;AAAA,MACxD,WAAW,sBAAsB;AAC/B,iBAAS,gBAAgB,GAAa,oBAAoB;AAAA,MAC5D;AACA,aAAO,IAAI,SAAS,QAAQ;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,MAAM;AACX,UAAI,UAAU;AACZ,eAAO,OAAO,SAAS,MAAM;AAAA,MAC/B;AAAA,IACF;AAAA,EACF,GAAG,CAAC,kBAAkB,iBAAiB,qBAAqB,oBAAoB,gBAAgB,YAAY,CAAC;AAC/G;AACA,IAAM,iCAAiC,CAAC,QAAQ,CAAC,MAAM;AACrD,QAAM,OAAO,yBAAyB,OAAO,WAAW;AACxD,SAAO;AACT;AAGA,IAAM,qBAAqB,QAAM;AAC/B,QAAM,UAAM,qBAAO,EAAE;AACrB,8BAAU,MAAM;AACd,QAAI,UAAU;AAAA,EAChB,GAAG,CAAC,EAAE,CAAC;AACP,SAAO;AACT;AAOA,IAAM,YAAY,MAAM;AACtB,QAAM,aAAS,yBAAW,aAAa;AACvC,MAAI,CAAC,OAAQ,OAAM,IAAI,MAAM,gEAAgE;AAC7F,SAAO;AACT;AAMA,IAAM,uBAAuB,cAAY;AACvC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,UAAU;AACd,QAAM,MAAM,mBAAmB,QAAQ;AACvC,8BAAU,MAAM;AACd,wBAAoB,IAAI,GAAG;AAC3B,WAAO,MAAM;AACX,0BAAoB,OAAO,GAAG;AAAA,IAChC;AAAA,EACF,GAAG,CAAC,CAAC;AACP;AAMA,IAAM,sBAAsB,cAAY;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,UAAU;AACd,QAAM,MAAM,mBAAmB,QAAQ;AACvC,8BAAU,MAAM;AACd,uBAAmB,IAAI,GAAG;AAC1B,WAAO,MAAM;AACX,yBAAmB,OAAO,GAAG;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,CAAC;AACP;AAMA,IAAM,wBAAwB,CAAC,KAAK,SAAS,sBAAsB,SAAS;AAC1E,QAAM,CAAC,eAAe,gBAAgB,QAAI,uBAAS,CAAC,CAAC;AACrD,8BAAU,MAAM;AACd,UAAM,SAAS,IAAI;AACnB,QAAI,UAAU,QAAQ,cAAc,OAAO;AACzC,uBAAiB,gCAAgC;AAAA,QAC/C,QAAQ,IAAI;AAAA,QACZ;AAAA,QACA;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AAAA,EACF,GAAG,CAAC,QAAQ,SAAS,CAAC;AACtB,SAAO;AACT;AAEA,IAAM,YAAqB,mBAAK,MAAM;AACpC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,UAAU;AACd,QAAM,UAAM,qBAAO,IAAI;AACvB,WAAS,MAAM;AACb,UAAM,OAAO,IAAI;AACjB,QAAI,CAAC,KAAM;AACX,UAAM,UAAU,MAAM,YAAY;AAClC,UAAM,WAAW,IAAI,eAAe;AACpC,aAAS,aAAa,YAAY,IAAI,gBAAgB,QAAQ,UAAU,CAAC,CAAC;AAC1E,aAAS,aAAa,SAAS,IAAI,gBAAgB,QAAQ,QAAQ,CAAC,CAAC;AACrE,SAAK,SAAS,QAAQ;AACtB,SAAK,WAAW;AAAA,EAClB,CAAC;AACD,SAAoB,aAAAA,QAAM,cAAc,SAAS,MAAmB,aAAAA,QAAM,cAAc,gBAAgB;AAAA,IACtG;AAAA,IACA,eAAe;AAAA,EACjB,GAAgB,aAAAA,QAAM,cAAc,qBAAqB;AAAA,IACvD,OAAO;AAAA,IACP,cAAc;AAAA,EAChB,CAAC,GAAgB,aAAAA,QAAM,cAAc,kBAAkB,IAAI,CAAC,CAAC;AAC/D,CAAC;AAQD,IAAM,uBAAuB,oBAAkB;AAC7C,MAAI;AACJ,QAAM,UAAU;AAAA,IACd,IAAI,QAAQ,MAAM;AAChB,UAAI,CAAC,UAAU;AACb,mBAAW,eAAe;AAAA,MAC5B;AACA,aAAO,QAAQ,IAAI,UAAU,IAAI;AAAA,IACnC;AAAA,IACA,IAAI,QAAQ,MAAM,OAAO;AACvB,UAAI,CAAC,UAAU;AACb,mBAAW,eAAe;AAAA,MAC5B;AACA,aAAO,QAAQ,IAAI,UAAU,MAAM,KAAK;AAAA,IAC1C;AAAA,EACF;AACA,QAAM,QAAQ,IAAI,MAAM,CAAC,GAAG,OAAO;AACnC,QAAM,QAAQ,MAAM;AAClB,eAAW;AAAA,EACb;AACA,QAAM,MAAM,iBAAe;AACzB,eAAW;AAAA,EACb;AAKA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,oBAA6B,4BAAc,MAAS;AAC1D,IAAM,gCAAgC,CAAC,QAAQ,UAAU;AACvD,MAAI,uBAAuB,uBAAuB,uBAAuB,uBAAuB,wBAAwB;AACxH,SAAO;AAAA,IACL,QAAQ;AAAA,MACN,WAAW,OAAO,UAAU;AAAA,MAC5B,UAAU,OAAO,SAAS;AAAA,MAC1B,iBAAiB,wBAAwB,OAAO,SAAS,WAAW,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AAAA,MAC9I,kBAAkB,wBAAwB,OAAO,UAAU,WAAW,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AAAA,IAClJ;AAAA,IACA,OAAO;AAAA,MACL,WAAW,MAAM,UAAU;AAAA,MAC3B,UAAU,MAAM,SAAS;AAAA,MACzB,iBAAiB,wBAAwB,MAAM,SAAS,WAAW,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AAAA,MAC7I,kBAAkB,wBAAwB,MAAM,UAAU,WAAW,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AAAA,IACjJ;AAAA,IACA,WAAW,MAAM,UAAU;AAAA,IAC3B,UAAU,MAAM,SAAS;AAAA,IACzB,iBAAiB,yBAAyB,MAAM,SAAS,WAAW,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB;AAAA,IAChJ,kBAAkB,yBAAyB,MAAM,UAAU,WAAW,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB;AAAA,EACpJ;AACF;AACA,IAAM,eAAe,YAAY;AAC/B,MAAI,IAAI,MAAM,OAAO,yBAA2B;AAChD,QAAM,EAAE,KAAK;AACb,SAAO;AACT;AAKA,IAAM,UAAU,WAAS;AACvB,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ;AAAA,IACA,WAAW,IAAI;AAAA,IACf,SAAS;AAAA,IACT,cAAc;AAAA,IACd;AAAA,IACA,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,UAAU,CAAC,GAAG,OAAO,CAAC;AAAA,IACtB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,0BAA0B;AAAA,IAC1B,aAAa;AAAA,EACf,IAAI;AACJ,QAAM,SAAS,QAAQ,cAAc,CAAC,sBAAsB,YAAY,CAAC;AACzE,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,SAAS;AACb,QAAM,kBAAkB,SAAS,MAAM,oBAAI,IAAI,CAAC;AAChD,QAAM,iBAAiB,SAAS,MAAM,oBAAI,IAAI,CAAC;AAC/C,QAAM,kBAAkB,SAAS,MAAM,oBAAI,IAAI,CAAC;AAChD,QAAM,iBAAiB,SAAS,MAAM,oBAAI,IAAI,CAAC;AAC/C,QAAM,aAAa,SAAS,MAAM,IAAI,GAAW,KAAK,CAAC;AACvD,QAAM,sBAAsB,SAAS,MAAM,oBAAI,IAAI,CAAC;AACpD,QAAM,qBAAqB,SAAS,MAAM,oBAAI,IAAI,CAAC;AAOnD,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,OAAO;AAAA,IACP,KAAK;AAAA,EACP,IAAI,SAAS,MAAM,qBAAqB,MAAM,IAAI,OAAO,MAAM,qBAAqB,OAAO,CAAC,CAAC,CAAC;AAC9F,8BAAU,MAAM;AACd,WAAO,MAAM;AACX,iBAAW,KAAK;AAChB,sBAAgB;AAAA,IAClB;AAAA,EACF,GAAG,CAAC,CAAC;AAGL,8BAAU,MAAM;AACd,eAAW,UAAU,sBAAsB,OAAO;AAClD,eAAW,sBAAsB,sBAAsB;AACvD,eAAW,sBAAsB,kCAAkC;AACnE,eAAW,sBAAsB,2BAA2B;AAC5D,eAAW,sBAAsB,+BAA+B;AAChE,eAAW,sBAAsB,gBAAgB;AACjD,eAAW,sBAAsB,iBAAiB;AAClD,eAAW,sBAAsB,+BAA+B;AAChE,eAAW,aAAa;AACxB,eAAW,sBAAsB,4BAA4B;AAAA,EAC/D,GAAG,CAAC,YAAY,GAAG,SAAS,qBAAqB,iCAAiC,0BAA0B,oBAAoB,eAAe,gBAAgB,oBAAoB,YAAY,uBAAuB,CAAC;AACvN,QAAM,kCAA8B,0BAAY,YAAU;AACxD,QAAI;AACJ,UAAM,WAAW,WAAW,YAAY,MAAM;AAC9C,UAAM,YAAY,eAAe,IAAI,MAAM;AAC3C,UAAM,gBAAgB,eAAe,IAAI,MAAM;AAC/C,UAAM,kBAAkB,aAAa,QAAQ,aAAa,SAAS,UAAU,mBAAmB,SAAS,OAAO,OAAO,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB;AACvL,UAAM,YAAY,oBAAoB,SAAY,WAAW,aAAa,eAAe,IAAI;AAC7F,UAAM,WAAW,aAAa,oBAAoB,SAAY,gBAAgB,IAAI,eAAe,IAAI;AACrG,UAAM,iBAAiB,oBAAoB,SAAY,gBAAgB,IAAI,eAAe,IAAI;AAC9F,UAAM,SAAS;AAAA,MACb,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,QAAM,CAAC,aAAa,QAAI,uBAAS;AAAA,IAC/B,eAAe,CAAC;AAAA,IAChB,aAAa;AAAA,EACf,CAAC;AACD,QAAM,WAAO,0BAAY,QAAM;AAC7B,UAAM,QAAQ;AAId,UAAM,mBAAmB,aAAa;AAOtC,UAAM,eAAe,UAAU,MAAM,IAAI,GAAG,GAAG;AAC/C,UAAM,YAAY,WAAS;AAEzB,0BAAoB,QAAQ,cAAY;AACtC,iBAAS,QAAQ,KAAK;AAAA,MACxB,CAAC;AACD,YAAM,WAAW;AACjB,YAAM,KAAK,UAAU;AAGrB,yBAAmB,QAAQ,cAAY;AACrC,iBAAS,QAAQ,KAAK;AAAA,MACxB,CAAC;AAAA,IACH;AACA,QAAI,kBAAkB;AACpB,gBAAU,YAAY;AAAA,IACxB,OAAO;AAGL,oBAAc,eAAe;AAC7B,aAAO,cAAc,eAAe,UAAU;AAG5C,YAAI,aAAa;AACf,wBAAc,gBAAgB,CAAC;AAC/B,gBAAM,iBAAiB,UAAQ;AAC7B,0BAAc,cAAc,KAAK,MAAM,IAAI;AAAA,cACzC,UAAU,KAAK,YAAY;AAAA,cAC3B,UAAU,KAAK,SAAS;AAAA,YAC1B;AAAA,UACF,CAAC;AAAA,QACH;AACA,kBAAU,QAAQ;AAClB,sBAAc,eAAe;AAAA,MAC/B;AAAA,IACF;AACA,UAAM,qBAAqB,oBAAoB,CAAC,eAAe,SAAS,IAAI,cAAc,cAAc;AAGxG,oBAAgB,QAAQ,CAAC,OAAO,WAAW;AACzC,YAAM,YAAY,MAAM,aAAa,MAAM;AAC3C,YAAM,SAAS,gBAAgB,IAAI,MAAM;AACzC,UAAI,WAAW,QAAQ,WAAW,UAAU,OAAO,WAAW,WAAW,QAAQ,WAAW,UAAU,OAAO,QAAQ;AACnH,YAAI,UAAU,WAAW,KAAK,CAAC,MAAM,YAAY;AAC/C,cAAI;AACJ,qBAAW,QAAQ,WAAW,SAAS,UAAU,kBAAkB,OAAO,aAAa,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,KAAK,MAAM;AAAA,QAClK;AACA,YAAI,CAAC,UAAU,WAAW,KAAK,MAAM,YAAY;AAC/C,cAAI;AACJ,qBAAW,QAAQ,WAAW,SAAS,UAAU,iBAAiB,OAAO,YAAY,QAAQ,mBAAmB,SAAS,SAAS,eAAe,KAAK,MAAM;AAAA,QAC9J;AACA,cAAM,aAAa,UAAU,WAAW;AAAA,MAC1C;AACA,UAAI,CAAC,aAAa,UAAU,WAAW,KAAK,EAAE,qBAAqB,MAAM,WAAW,CAAC,MAAM,WAAW;AACpG;AAAA,MACF;AAGA,UAAI,IAAI,UAAU,YAAY;AAC9B,UAAI,IAAI,UAAU,SAAS;AAC3B,UAAI,gBAAgB,cAAc,cAAc,MAAM;AACtD,UAAI,eAAe;AAEjB,iBAAS,QAAQ,cAAc,UAAU,6BAA6B,cAAc,QAAQ,GAAG,MAAM,KAAK,EAAE,YAAY,MAAM,mBAAmB,EAAE,UAAU,WAAW,WAAW,MAAM;AAGzL,YAAI,MAAM,YAAY,QAAQ;AAC5B,gBAAM,OAAO,SAAS,KAAK,SAAS;AACpC,gBAAM,OAAO,WAAW,KAAK,SAAS;AAAA,QACxC;AAAA,MACF;AAGA,eAAS,QAAQ,GAAG,6BAA6B,CAAC,GAAG,MAAM,KAAK,EAAE,YAAY,MAAM,mBAAmB,EAAE,UAAU,WAAW,WAAW,MAAM;AAC/I,UAAI,MAAM,YAAY,iBAAiB;AACrC,cAAM,UAAU,QAAQ;AAAA,MAC1B,OAAO;AAEL,cAAM,OAAO,SAAS,KAAK,WAAW,kBAAkB;AACxD,cAAM,OAAO,WAAW,MAAM,WAAW,kBAAkB;AAAA,MAC7D;AAAA,IACF,CAAC;AACD,eAAW,qBAAqB,CAAC,SAAS,SAAS,YAAY;AAC7D,YAAM,UAAU,4BAA4B,OAAO;AACnD,YAAM,UAAU,4BAA4B,OAAO;AAGnD,UAAI,EAAE,YAAY,QAAQ,YAAY,UAAU,QAAQ,SAAS,WAAW,EAAE,YAAY,QAAQ,YAAY,UAAU,QAAQ,SAAS,SAAS;AAChJ;AAAA,MACF;AACA,YAAM,oBAAoB,8BAA8B,SAAS,OAAO;AACxE,YAAM,oBAAoB,8BAA8B,SAAS,OAAO;AACxE,UAAI,SAAS;AACX,cAAM,YAAY,QAAQ,SAAS,QAAQ,QAAQ,SAAS,QAAQ,CAAC,UAAU,YAAY;AACzF,cAAI,uBAAuB,wBAAwB,uBAAuB,wBAAwB,uBAAuB,wBAAwB,uBAAuB;AAExK,WAAC,wBAAwB,QAAQ,UAAU,YAAY,QAAQ,0BAA0B,SAAS,UAAU,yBAAyB,sBAAsB,sBAAsB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,uBAAuB,eAAe,eAAe,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG;AAAA,YACjV;AAAA,YACA;AAAA,UACF,CAAC,CAAC;AACF,WAAC,wBAAwB,QAAQ,UAAU,YAAY,QAAQ,0BAA0B,SAAS,UAAU,yBAAyB,sBAAsB,sBAAsB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,uBAAuB,eAAe,eAAe,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG;AAAA,YACjV;AAAA,YACA;AAAA,UACF,CAAC,CAAC;AAGF,WAAC,wBAAwB,QAAQ,SAAS,YAAY,QAAQ,0BAA0B,SAAS,UAAU,yBAAyB,sBAAsB,sBAAsB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,uBAAuB,eAAe,eAAe,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG;AAAA,YAChV;AAAA,YACA;AAAA,UACF,CAAC,CAAC;AACF,WAAC,wBAAwB,QAAQ,SAAS,YAAY,QAAQ,0BAA0B,SAAS,UAAU,yBAAyB,sBAAsB,sBAAsB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,uBAAuB,eAAe,eAAe,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG;AAAA,YAChV;AAAA,YACA;AAAA,UACF,CAAC,CAAC;AAAA,QACJ,CAAC;AAAA,MACH,OAAO;AACL,YAAI,wBAAwB,wBAAwB,wBAAwB,wBAAwB,wBAAwB,wBAAwB,wBAAwB;AAC5K,SAAC,yBAAyB,QAAQ,UAAU,YAAY,QAAQ,2BAA2B,SAAS,UAAU,yBAAyB,uBAAuB,qBAAqB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,wBAAwB,iBAAiB;AAC7S,SAAC,yBAAyB,QAAQ,UAAU,YAAY,QAAQ,2BAA2B,SAAS,UAAU,yBAAyB,uBAAuB,qBAAqB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,wBAAwB,iBAAiB;AAC7S,SAAC,yBAAyB,QAAQ,SAAS,YAAY,QAAQ,2BAA2B,SAAS,UAAU,yBAAyB,uBAAuB,qBAAqB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,wBAAwB,iBAAiB;AAC5S,SAAC,yBAAyB,QAAQ,SAAS,YAAY,QAAQ,2BAA2B,SAAS,UAAU,yBAAyB,uBAAuB,qBAAqB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,wBAAwB,iBAAiB;AAAA,MAC9S;AAGA,UAAI,SAAS;AACX,YAAI,MAAM,iBAAiB,QAAQ,SAAS,QAAQ,QAAQ,SAAS,MAAM,GAAG;AAC5E,cAAI,wBAAwB,wBAAwB,wBAAwB,wBAAwB,wBAAwB,wBAAwB,wBAAwB;AAC5K,WAAC,yBAAyB,QAAQ,UAAU,YAAY,QAAQ,2BAA2B,SAAS,UAAU,yBAAyB,uBAAuB,yBAAyB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,wBAAwB,iBAAiB;AACjT,WAAC,yBAAyB,QAAQ,UAAU,YAAY,QAAQ,2BAA2B,SAAS,UAAU,yBAAyB,uBAAuB,yBAAyB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,wBAAwB,iBAAiB;AACjT,WAAC,yBAAyB,QAAQ,SAAS,YAAY,QAAQ,2BAA2B,SAAS,UAAU,yBAAyB,uBAAuB,yBAAyB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,wBAAwB,iBAAiB;AAChT,WAAC,yBAAyB,QAAQ,SAAS,YAAY,QAAQ,2BAA2B,SAAS,UAAU,yBAAyB,uBAAuB,yBAAyB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,wBAAwB,iBAAiB;AAAA,QAClT;AAAA,MACF,OAAO;AACL,YAAI,wBAAwB,wBAAwB,wBAAwB,wBAAwB,wBAAwB,wBAAwB,wBAAwB;AAC5K,SAAC,yBAAyB,QAAQ,UAAU,YAAY,QAAQ,2BAA2B,SAAS,UAAU,yBAAyB,uBAAuB,wBAAwB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,wBAAwB,iBAAiB;AAChT,SAAC,yBAAyB,QAAQ,UAAU,YAAY,QAAQ,2BAA2B,SAAS,UAAU,yBAAyB,uBAAuB,wBAAwB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,wBAAwB,iBAAiB;AAChT,SAAC,yBAAyB,QAAQ,SAAS,YAAY,QAAQ,2BAA2B,SAAS,UAAU,yBAAyB,uBAAuB,wBAAwB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,wBAAwB,iBAAiB;AAC/S,SAAC,yBAAyB,QAAQ,SAAS,YAAY,QAAQ,2BAA2B,SAAS,UAAU,yBAAyB,uBAAuB,wBAAwB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,wBAAwB,iBAAiB;AAAA,MACjT;AAAA,IACF,CAAC;AACD,eAAW,wBAAwB,WAAS;AAC1C,UAAI,wBAAwB,yBAAyB,wBAAwB,yBAAyB,wBAAwB,yBAAyB,wBAAwB;AAC/K,YAAM,UAAU,4BAA4B,MAAM,UAAU,CAAC;AAC7D,YAAM,UAAU,4BAA4B,MAAM,UAAU,CAAC;AAG7D,UAAI,EAAE,YAAY,QAAQ,YAAY,UAAU,QAAQ,SAAS,WAAW,EAAE,YAAY,QAAQ,YAAY,UAAU,QAAQ,SAAS,SAAS;AAChJ;AAAA,MACF;AACA,YAAM,oBAAoB,8BAA8B,SAAS,OAAO;AACxE,YAAM,oBAAoB,8BAA8B,SAAS,OAAO;AACxE,OAAC,yBAAyB,QAAQ,UAAU,YAAY,QAAQ,2BAA2B,SAAS,UAAU,0BAA0B,uBAAuB,oBAAoB,QAAQ,4BAA4B,SAAS,SAAS,wBAAwB,KAAK,wBAAwB,eAAe,eAAe,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG;AAAA,QACtV,YAAY,MAAM,WAAW;AAAA,QAC7B,qBAAqB,MAAM,oBAAoB;AAAA,QAC/C,mBAAmB,MAAM,kBAAkB;AAAA,QAC3C,mBAAmB,MAAM,kBAAkB;AAAA,MAC7C,CAAC,CAAC;AACF,OAAC,yBAAyB,QAAQ,UAAU,YAAY,QAAQ,2BAA2B,SAAS,UAAU,0BAA0B,uBAAuB,oBAAoB,QAAQ,4BAA4B,SAAS,SAAS,wBAAwB,KAAK,wBAAwB,eAAe,eAAe,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG;AAAA,QACtV,YAAY,MAAM,WAAW;AAAA,QAC7B,qBAAqB,MAAM,oBAAoB;AAAA,QAC/C,mBAAmB,MAAM,kBAAkB;AAAA,QAC3C,mBAAmB,MAAM,kBAAkB;AAAA,MAC7C,CAAC,CAAC;AACF,OAAC,yBAAyB,QAAQ,SAAS,YAAY,QAAQ,2BAA2B,SAAS,UAAU,0BAA0B,uBAAuB,oBAAoB,QAAQ,4BAA4B,SAAS,SAAS,wBAAwB,KAAK,wBAAwB,eAAe,eAAe,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG;AAAA,QACrV,YAAY,MAAM,WAAW;AAAA,QAC7B,qBAAqB,MAAM,oBAAoB;AAAA,QAC/C,mBAAmB,MAAM,kBAAkB;AAAA,QAC3C,mBAAmB,MAAM,kBAAkB;AAAA,MAC7C,CAAC,CAAC;AACF,OAAC,yBAAyB,QAAQ,SAAS,YAAY,QAAQ,2BAA2B,SAAS,UAAU,0BAA0B,uBAAuB,oBAAoB,QAAQ,4BAA4B,SAAS,SAAS,wBAAwB,KAAK,wBAAwB,eAAe,eAAe,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG;AAAA,QACrV,YAAY,MAAM,WAAW;AAAA,QAC7B,qBAAqB,MAAM,oBAAoB;AAAA,QAC/C,mBAAmB,MAAM,kBAAkB;AAAA,QAC3C,mBAAmB,MAAM,kBAAkB;AAAA,MAC7C,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,UAAM,uBAAuB,MAAM;AACjC,iBAAW;AAAA,IACb,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,UAAU,aAAa,UAAU,CAAC;AAC9C,QAAM,cAAU,sBAAQ,OAAO;AAAA,IAC7B;AAAA,IACA,OAAO;AAAA,IACP,UAAU,WAAS;AACjB,oBAAc,KAAK;AAAA,IACrB;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,SAAS;AAAA,IACT;AAAA,EACF,IAAI,CAAC,QAAQ,MAAM,OAAO,WAAW,OAAO,CAAC;AAC7C,QAAM,mBAAe,0BAAY,WAAS;AACxC,QAAI,CAAC,QAAQ;AACX,WAAK,KAAK;AAAA,IACZ;AAAA,EACF,GAAG,CAAC,QAAQ,IAAI,CAAC;AACjB,SAAoB,aAAAA,QAAM,cAAc,cAAc,UAAU;AAAA,IAC9D,OAAO;AAAA,EACT,GAAgB,aAAAA,QAAM,cAAc,gBAAgB;AAAA,IAClD,QAAQ;AAAA,IACR,MAAM;AAAA,IACN;AAAA,EACF,CAAC,GAAG,SAAsB,aAAAA,QAAM,cAAc,OAAO,IAAI,GAAG,QAAQ;AACtE;AAEA,SAAS,WAAW;AAClB,SAAO,WAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AACpE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,IAAI,UAAU,CAAC;AACnB,eAAS,KAAK,EAAG,EAAC,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAChE;AACA,WAAO;AAAA,EACT,GAAG,SAAS,MAAM,MAAM,SAAS;AACnC;AAGA,IAAM,kBAAkB,CAAC,cAAc,eAAe,SAAS;AAC7D,QAAM,eAAW,qBAAO,YAAY;AAGpC,MAAI,gBAAgB,OAAO,iBAAiB,YAAY;AACtD,QAAI,CAAC,aAAa,SAAS;AACzB,mBAAa,UAAU,SAAS;AAAA,IAClC;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAKA,IAAM,wBAAwB,CAAC,UAAU,WAAW,mBAAmB;AACrE,QAAM,UAAM,qBAAO,MAAS;AAC5B,QAAM,kBAAc,0BAAY,MAAM;AACpC,QAAI,CAAC,IAAI,SAAS;AAChB,UAAI,UAAU,SAAS;AAAA,IACzB;AACA,WAAO,IAAI;AAAA,EACb,GAAG,cAAc;AACjB,8BAAU,MAAM;AAEd,UAAM,WAAW,YAAY;AAC7B,UAAM,UAAU,MAAM,UAAU,QAAQ;AACxC,WAAO,MAAM;AACX,cAAQ;AACR,UAAI,UAAU;AAAA,IAChB;AAAA,EACF,GAAG,CAAC,WAAW,CAAC;AAChB,SAAO;AACT;AAEA,IAAM,2BAA2B,aAAW;AAC1C,MAAI;AACJ,QAAM,OAAO,yBAAyB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,SAAS,SAAS;AAClH,QAAM,OAAO,IAAI,GAAc,IAAI;AAGnC,OAAK,YAAY,oBAAoB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,QAAQ,sBAAsB,SAAS,oBAAoB;AACxK,SAAO;AACT;AACA,IAAM,uBAAuB,CAAC;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU,YAAY;AACxB,MAAM;AACJ,SAAO,kBAAkB,MAAM,KAAK;AACpC,QAAM,sBAAsB,OAAO,OAAO,YAAY,MAAM,EAAE,OAAO;AACrE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW,YAAY,YAAY,YAAU;AAC3C,aAAO,OAAO,KAAK,MAAM;AAAA,IAC3B;AAAA,IACA,WAAW,YAAY,YAAY,YAAU,OAAO,KAAK,OAAO,MAAM;AAAA,IACtE,OAAO,cAAc,OAAO,cAAc,MAAM,EAAE,MAAM;AAAA,IACxD,YAAY;AAAA,IACZ,UAAU;AAAA,EACZ;AACF;AACA,IAAM,4BAA4B,CAAC,QAAQ,aAAa,UAAU;AAClE,IAAM,0BAA0B;AAAA,EAC9B,cAAc,CAAC,IAAI,UAAU;AAC3B,OAAG,gBAAgB,OAAO,IAAI;AAAA,EAChC;AAAA,EACA,2BAA2B,IAAI,OAAO;AACpC,OAAG,8BAA8B,KAAK;AAAA,EACxC;AAAA,EACA,eAAe,CAAC,IAAI,UAAU;AAC5B,OAAG,iBAAiB,KAAK;AAAA,EAC3B;AAAA,EACA,gBAAgB,CAAC,IAAI,UAAU;AAC7B,OAAG,kBAAkB,KAAK;AAAA,EAC5B;AAAA,EACA,gBAAgB,CAAC,IAAI,UAAU;AAC7B,OAAG,kBAAkB,KAAK;AAAA,EAC5B;AAAA,EACA,kBAAkB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM;AACnC,OAAG,oBAAoB,GAAG,GAAG,GAAG,IAAI;AAAA,EACtC;AAAA,EACA,qBAAqB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM;AACtC,OAAG,uBAAuB,GAAG,GAAG,GAAG,IAAI;AAAA,EACzC;AAAA,EACA,eAAe,CAAC,IAAI,UAAU;AAC5B,OAAG,cAAc,OAAO,IAAI;AAAA,EAC9B;AAAA,EACA,kBAAkB,CAAC,IAAI,UAAU;AAC/B,OAAG,iBAAiB,OAAO,IAAI;AAAA,EACjC;AAAA,EACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM;AAClC,OAAG,UAAU;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG,IAAI;AAAA,EACT;AAAA,EACA,gBAAgB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM;AACjC,OAAG,UAAU;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG,IAAI;AAAA,EACT;AAAA,EACA,KAAK,CAAC,IAAI,UAAU;AAClB,OAAG,UAAU,KAAK;AAAA,EACpB;AAAA,EACA,mBAAmB,CAAC,IAAI,UAAU;AAChC,OAAG,qBAAqB,KAAK;AAAA,EAC/B;AAAA,EACA,UAAU,CAAC,IAAI,UAAU;AACvB,OAAG,WAAW;AAAA,EAChB;AAAA,EACA,KAAK,IAAI,OAAO;AACd,OAAG,YAAY,wBAAwB,KAAK,GAAG,IAAI;AAAA,EACrD;AAAA,EACA,UAAU,MAAM;AAAA,EAAC;AAAA,EACjB,UAAU,MAAM;AAAA,EAAC;AAAA,EACjB,YAAY,MAAM;AAAA,EAAC;AAAA,EACnB,OAAO,MAAM;AAAA,EAAC;AAChB;AACA,IAAM,6BAA6B,OAAO,KAAK,uBAAuB;AACtE,IAAM,sBAAsB,CAAC,WAAW,SAAS,QAAQ,qBAAqB,SAAS;AACrF,MAAI,CAAC,WAAW;AACd;AAAA,EACF;AACA,QAAM,QAAQ,OAAO,IAAI,UAAU,MAAM;AACzC,MAAI,OAAO;AACT,QAAI,oBAAoB;AACtB,YAAM,OAAO,kBAAkB,MAAM,KAAK;AAC1C,eAAS,KAAK,MAAM,OAAO,WAAW,EAAE,UAAU,WAAW,WAAW,MAAM;AAC9E,gBAAU,eAAe,WAAW,KAAK;AACzC,gBAAU,YAAY,WAAW,KAAK;AAAA,IACxC;AACA,+BAA2B,QAAQ,SAAO;AACxC,UAAI,OAAO,SAAS;AAClB,gCAAwB,GAAG,EAAE,WAAW,QAAQ,GAAG,CAAC;AAAA,MACtD;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,IAAM,4BAA4B,CAAC,cAAc,OAAO,QAAQ,qBAAqB,SAAS;AAE5F,QAAM,8BAA0B,sBAAQ,MAAM,2BAA2B,QAAQ,SAAO;AACtF,WAAO,cAAc,MAAM,GAAG,CAAC;AAAA,EACjC,CAAC,GAAG,CAAC,KAAK,CAAC;AACX,8BAAU,MAAM;AACd,UAAM,YAAY,aAAa;AAC/B,wBAAoB,WAAW,OAAO,QAAQ,kBAAkB;AAAA,EAClE,GAAG,uBAAuB;AAC5B;AACA,IAAM,qBAAqB,CAAC,cAAc,OAAO,WAAW;AAC1D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,8BAAU,MAAM;AACd,UAAM,YAAY,aAAa;AAC/B,WAAO,IAAI,UAAU,QAAQ,aAAa;AAC1C,WAAO,MAAM;AACX,aAAO,OAAO,UAAU,MAAM;AAAA,IAChC;AAAA,EACF,GAAG,CAAC,QAAQ,SAAS,kBAAkB,iBAAiB,qBAAqB,oBAAoB,cAAc,CAAC;AAClH;AAMA,IAAM,OAAO,CAAC;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AACF,IAAI;AAAA,EACF,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL,MAAM;AACJ,SAAO,IAAI,QAAQ,GAAG,GAAG,CAAC;AAC5B;AAMA,IAAM,OAAO,CAAC;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI;AAAA,EACF,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL,MAAM;AACJ,SAAO,IAAI,WAAW,GAAG,GAAG,GAAG,CAAC;AAClC;AAMA,IAAM,QAAQ,CAAC;AAAA,EACb;AAAA,EACA;AAAA,EACA;AACF,IAAI;AAAA,EACF,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL,MAAM;AACJ,SAAO,IAAI,MAAM,GAAG,GAAG,CAAC;AAC1B;AAMA,IAAM,kBAA2B,mBAAK,WAAS;AAC7C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,UAAU;AACd,QAAM,mBAAmB,oBAAoB;AAC7C,QAAM,cAAc,gBAAgB,MAAM,GAAG;AAC7C,QAAM,gBAAY,qBAAO,IAAI;AAG7B,QAAM,qBAAqB,yBAAyB,QAAQ;AAAA;AAAA,IAE5D,MAAM,QAAQ,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC;AAAA,GAAC;AACrD,QAAM,cAAc,sBAAsB,MAAM;AAC9C,UAAM,aAAa,UAAU,QAAQ,cAAc,KAAK,CAAC;AACzD,UAAM,WAAW,0BAA0B,OAAO,OAAO,YAAY,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,YAAY;AACtK,QAAI,OAAO,MAAM,OAAO,YAAY;AAClC,YAAM,IAAI,QAAQ;AAAA,IACpB;AACA,gBAAY,UAAU;AACtB,WAAO;AAAA,EACT,GAAG,cAAY;AACb,QAAI,MAAM,YAAY,SAAS,MAAM,GAAG;AACtC,YAAM,eAAe,UAAU,IAAI;AAAA,IACrC;AAAA,EACF,GAAG,CAAC,GAAG,oBAAoB,gBAAgB,CAAC;AAC5C,8BAAU,MAAM;AACd,UAAM,WAAW,YAAY;AAC7B,mBAAe,IAAI,SAAS,QAAQ,oBAAoB,UAAU,UAAU,SAAS,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,IAAI,OAAO,CAAC;AACtL,WAAO,MAAM;AACX,qBAAe,OAAO,SAAS,MAAM;AAAA,IACvC;AAAA,EACF,GAAG,CAAC,WAAW,CAAC;AAChB,QAAM,kBAAc,sBAAQ,MAAM;AAChC,WAAO,eAAe,eAAe,CAAC,GAAG,+BAA+B,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,OAAO,CAAC,GAAG,KAAK;AAAA,EAC/K,GAAG,CAAC,OAAO,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,OAAO,CAAC;AACxG,2BAAyB,aAAa,aAAa,cAAc;AACjE,oBAAkB,aAAa,aAAa,gBAAgB,kCAAkC,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,OAAO,CAAC;AAC3L,SAAoB,aAAAA,QAAM,cAAc,YAAY;AAAA,IAClD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL;AAAA,EACF,GAAG,QAAQ;AACb,CAAC;AAKD,IAAM,iBAA8B,aAAAA,QAAM,WAAW,CAAC,OAAO,QAAQ;AACnE,SAAoB,aAAAA,QAAM,cAAc,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACvE,OAAO;AAAA,IACP;AAAA,EACF,CAAC,CAAC;AACJ,CAAC;AACD,eAAe,cAAc;AAK7B,IAAM,sBAAmC,aAAAA,QAAM,WAAW,CAAC,OAAO,QAAqB,aAAAA,QAAM,cAAc,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,EAC1I,OAAO;AAAA,EACP;AACF,CAAC,CAAC,CAAC;AACH,oBAAoB,cAAc;AAKlC,IAAM,eAA4B,aAAAA,QAAM,WAAW,CAAC,OAAO,QAAqB,aAAAA,QAAM,cAAc,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,EACnI,OAAO;AAAA,EACP;AACF,CAAC,CAAC,CAAC;AACH,aAAa,cAAc;AAK3B,IAAM,kBAA+B,aAAAA,QAAM,WAAW,CAAC,OAAO,QAAqB,aAAAA,QAAM,cAAc,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,EACtI,OAAO;AAAA,EACP;AACF,CAAC,CAAC,CAAC;AACH,gBAAgB,cAAc;AAK9B,IAAM,sBAAmC,aAAAA,QAAM,WAAW,CAAC,OAAO,QAAqB,aAAAA,QAAM,cAAc,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,EAC1I,OAAO;AAAA,EACP;AACF,CAAC,CAAC,CAAC;AACH,oBAAoB,cAAc;AAKlC,IAAM,kBAA+B,aAAAA,QAAM,WAAW,CAAC,OAAO,QAAqB,aAAAA,QAAM,cAAc,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,EACtI,OAAO;AAAA,EACP;AACF,CAAC,CAAC,CAAC;AACH,gBAAgB,cAAc;AAK9B,IAAM,eAA4B,aAAAA,QAAM,WAAW,CAAC,OAAO,QAAqB,aAAAA,QAAM,cAAc,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,EACnI,OAAO;AAAA,EACP;AACF,CAAC,CAAC,CAAC;AACH,aAAa,cAAc;AAK3B,IAAM,oBAAiC,aAAAA,QAAM,WAAW,CAAC,OAAO,QAAqB,aAAAA,QAAM,cAAc,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,EACxI,OAAO;AAAA,EACP;AACF,CAAC,CAAC,CAAC;AACH,kBAAkB,cAAc;AAKhC,IAAM,mBAAgC,aAAAA,QAAM,WAAW,CAAC,OAAO,QAAqB,aAAAA,QAAM,cAAc,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,EACvI,OAAO;AAAA,EACP;AACF,CAAC,CAAC,CAAC;AACH,iBAAiB,cAAc;AAK/B,IAAM,wBAAqC,aAAAA,QAAM,WAAW,CAAC,OAAO,QAAqB,aAAAA,QAAM,cAAc,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,EAC5I,OAAO;AAAA,EACP;AACF,CAAC,CAAC,CAAC;AACH,iBAAiB,cAAc;AAK/B,IAAM,qBAAkC,aAAAA,QAAM,WAAW,CAAC,OAAO,QAAqB,aAAAA,QAAM,cAAc,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,EACzI,OAAO;AAAA,EACP;AACF,CAAC,CAAC,CAAC;AACH,mBAAmB,cAAc;AAEjC,IAAM,cAAc,CAAC,OAAO,YAAY,QAAQ,YAAY,YAAY,SAAS,cAAc,gBAAgB;AAC/G,IAAM,uBAAgC,4BAAc,MAAS;AAC7D,IAAM,sBAAsB,UAAM,yBAAW,gBAAgB;AAK7D,IAAM,gBAAyB,mBAAK,WAAS;AAC3C,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,cAAc,yBAAyB,OAAO,WAAW;AAC3D,QAAM,gBAAY,qBAAO,IAAI;AAC7B,QAAM,eAAe,gBAAgB,GAAG;AACxC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,UAAU;AACd,QAAM,oBAAgB,sBAAQ,MAAM;AAClC,WAAO,eAAe,eAAe,eAAe,CAAC,GAAG,cAAc,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,MACnF,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,GAAG,CAAC,gBAAgB,KAAK,CAAC;AAC1B,QAAM,qBAAqB,0BAA0B,QAAQ,SAAO;AAClE,WAAO,MAAM,QAAQ,cAAc,GAAG,CAAC,IAAI,CAAC,GAAG,cAAc,GAAG,CAAC,IAAI,cAAc,GAAG;AAAA,EACxF,CAAC;AACD,QAAM,qBAAqB,sBAAsB,WAAW,aAAa;AAGzE,QAAM,eAAe,sBAAsB,MAAM;AAC/C,UAAM,OAAO,yBAAyB,aAAa;AACnD,UAAM,YAAY,MAAM,gBAAgB,IAAI;AAC5C,QAAI,OAAO,QAAQ,YAAY;AAC7B,UAAI,SAAS;AAAA,IACf;AACA,iBAAa,UAAU;AACvB,WAAO;AAAA,EACT,GAAG,eAAa;AACd,QAAI,MAAM,aAAa,UAAU,MAAM,GAAG;AACxC,YAAM,gBAAgB,SAAS;AAAA,IACjC;AAAA,EACF,GAAG,kBAAkB;AAGrB,8BAAU,MAAM;AACd,UAAM,YAAY,aAAa;AAC/B,UAAM,QAAQ,qBAAqB;AAAA,MACjC;AAAA,MACA,QAAQ,UAAU;AAAA,IACpB,CAAC;AACD,oBAAgB,IAAI,UAAU,QAAQ,MAAM,iBAAiB,MAAM,eAAe,KAAK,IAAI,KAAK;AAChG,WAAO,MAAM;AACX,sBAAgB,OAAO,UAAU,MAAM;AAAA,IACzC;AAAA,EACF,GAAG,CAAC,YAAY,CAAC;AACjB,4BAA0B,cAAc,eAAe,eAAe;AACtE,qBAAmB,cAAc,eAAe,eAAe;AAC/D,QAAM,mBAAe,sBAAQ,MAAM;AACjC,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,SAAS;AAAA,IACX;AAAA,EACF,GAAG,CAAC,YAAY,CAAC;AACjB,SAAoB,aAAAA,QAAM,cAAc,iBAAiB,UAAU;AAAA,IACjE,OAAO;AAAA,EACT,GAAgB,aAAAA,QAAM,cAAc,YAAY,SAAS;AAAA,IACvD,KAAK;AAAA,EACP,GAAG,aAAa;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,GAAG,UAAU,mBAAmB,IAAI,CAAC,eAAe,UAAuB,aAAAA,QAAM,cAAc,aAAa,SAAS;AAAA,IACpH,KAAK;AAAA,EACP,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;AACtB,CAAC;AACD,UAAU,cAAc;AAMxB,IAAM,mBAA4B,mBAAK,WAAS;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,UAAU;AACd,QAAM,aAAS,qBAAO,IAAI;AAC1B,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,oBAAoB;AACxB,QAAM,oBAAgB,sBAAQ,MAAM;AAClC,WAAO,eAAe,eAAe,eAAe,CAAC,GAAG,cAAc,GAAG,OAAO,GAAG,CAAC,GAAG;AAAA,MACrF,UAAU;AAAA,MACV,WAAW;AAAA,IACb,CAAC;AAAA,EACH,GAAG,CAAC,gBAAgB,OAAO,CAAC;AAC5B,QAAM,qBAAqB,sBAAsB,QAAQ,eAAe,KAAK;AAC7E,SAAoB,aAAAA,QAAM,cAAc,YAAY;AAAA,IAClD,KAAK;AAAA,IACL,UAAU;AAAA,MACR,cAAc;AAAA,IAChB;AAAA,EACF,GAAG,UAAU,mBAAmB,IAAI,CAAC,eAAe,UAAuB,aAAAA,QAAM,cAAc,aAAa,SAAS;AAAA,IACnH,KAAK;AAAA,EACP,GAAG,aAAa,CAAC,CAAC,CAAC;AACrB,CAAC;AACD,aAAa,cAAc;AAE3B,IAAM,YAAY,CAAC,KAAK;AAAxB,IACE,aAAa,CAAC,YAAY,aAAa,iBAAiB,YAAY,YAAY,cAAc,OAAO;AACvG,IAAM,2BAAoC,mBAAK,UAAQ;AACrD,MAAI;AAAA,IACA;AAAA,EACF,IAAI,MACJ,QAAQ,yBAAyB,MAAM,SAAS;AAClD,QAAM,iBAAiB,gBAAgB,KAAK,CAAC,CAAC;AAC9C,QAAM,gBAAY,qBAAO,IAAI;AAC7B,QAAM,yBAAqB,qBAAO,IAAI;AACtC,QAAM;AAAA;AAAA,IAEF;AAAA,IACA;AAAA,IACA,gBAAgB,CAAC;AAAA;AAAA,IAEjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,EAGF,IAAI,OACJ,iBAAiB,yBAAyB,OAAO,UAAU;AAC7D,QAAM,qBAAqB,sBAAsB,WAAW,eAAe,eAAe,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,IACxG,UAAU;AAAA,EACZ,CAAC,CAAC;AACF,QAAM,mBAAmB,MAAM;AAC7B,UAAM,aAAa,mBAAmB,QAAQ,SAAS,CAAC;AACxD,QAAI,cAAc,qBAAqB,YAAY;AACjD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,8BAAU,MAAM;AACd,UAAM,gBAAgB,iBAAiB;AACvC,QAAI,eAAe;AACjB,oBAAc,eAAe,SAAS,gBAAgB;AAAA,IACxD,OAAO;AACL,cAAQ,KAAK,gFAAgF;AAAA,IAC/F;AAAA,EACF,GAAG,CAAC,CAAC;AAGL,QAAM,sBAAsB,CAAC,OAAO,UAAU;AAC5C,UAAM,gBAAgB,iBAAiB;AACvC,QAAI,eAAe;AACjB,aAAO,eAAe,eAAe,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QACnD,WAAW,YAAU;AACnB,wBAAc,YAAY,OAAO,MAAM;AACvC,iBAAO;AAAA,QACT;AAAA,QACA,WAAW,YAAU;AACnB,wBAAc,YAAY,OAAO,MAAM;AACvC,wBAAc,eAAe,cAAc;AAAA,QAC7C;AAAA,QACA,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACA,SAAoB,aAAAA,QAAM,cAAc,YAAY,SAAS;AAAA,IAC3D,KAAK;AAAA,EACP,GAAG,gBAAgB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,GAAgB,aAAAA,QAAM,cAAc,YAAY;AAAA,IAC/C,KAAK;AAAA,EACP,GAAG,QAAQ,GAAG,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,IAAI,CAAC,UAAU,UAAuB,aAAAA,QAAM,cAAc,WAAW,SAAS,CAAC,GAAG,gBAAgB,UAAU;AAAA,IACxL,KAAK,UAAQ;AACX,qBAAe,QAAQ,KAAK,IAAI;AAAA,IAClC;AAAA,IACA,gBAAgB,WAAS,oBAAoB,OAAO,KAAK;AAAA,EAC3D,CAAC,GAAgB,aAAAA,QAAM,cAAc,aAAAA,QAAM,UAAU,MAAM,cAAc,IAAI,CAAC,MAAMC,WAAuB,aAAAD,QAAM,cAAc,uBAAU;AAAA,IACvI,KAAKC;AAAA,EACP,GAAG,IAAI,CAAC,GAAG,mBAAmB,IAAI,CAAC,eAAe,kBAA+B,aAAAD,QAAM,cAAc,aAAa,SAAS;AAAA,IACzH,KAAK;AAAA,EACP,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB,CAAC;AACD,qBAAqB,cAAc;AAKnC,IAAM,kBAAkB,CAAC,OAAO,OAAO,WAAW;AAChD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,UAAU;AACd,QAAM,eAAW,qBAAO,MAAS;AACjC,wBAAsB,MAAM;AAC1B,QAAI,MAAM,WAAW,MAAM,SAAS;AAClC,YAAM,WAAW,MAAM,mBAAmB,QAAQ,MAAM,SAAS,MAAM,SAAS,IAAI;AACpF,eAAS,UAAU;AACnB,aAAO;AAAA,IACT;AAAA,EACF,GAAG,WAAS;AACV,QAAI,OAAO;AACT,eAAS,UAAU;AACnB,UAAI,MAAM,gBAAgB,MAAM,MAAM,GAAG;AACvC,cAAM,mBAAmB,OAAO,IAAI;AAAA,MACtC;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AACL,SAAO;AACT;AASA,IAAM,gBAAgB,CAAC,OAAO,OAAO,CAAC,aAAa,iBAAiB,aAAa,eAAe,MAAM;AACpG,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,UAAU;AACd,SAAO,gBAAgB,OAAO,OAAO,OAAO,UAAU,MAAM,sBAAsB,WAAW,GAAG,6BAA6B,eAAe,GAAG,sBAAsB,WAAW,GAAG,6BAA6B,eAAe,CAAC,CAAC;AACnO;AAUA,IAAM,oBAAoB,CAAC,OAAO,OAAO,CAAC,aAAa,WAAW,MAAM;AACtE,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,UAAU;AACd,SAAO,gBAAgB,OAAO,OAAO,OAAO,UAAU,UAAU,sBAAsB,WAAW,GAAG,sBAAsB,WAAW,CAAC,CAAC;AACzI;AASA,IAAM,mBAAmB,CAAC,OAAO,OAAO,CAAC,aAAa,aAAa,MAAM,MAAM,MAAM;AACnF,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,UAAU;AACd,QAAM,SAAS,OAAO,UAAU,SAAS,sBAAsB,WAAW,GAAG,sBAAsB,WAAW,GAAG,sBAAsB,IAAI,CAAC;AAC5I,MAAI,QAAQ;AACV,WAAO,gBAAgB;AACvB,WAAO,SAAS;AAAA,EAClB;AACA,SAAO,gBAAgB,OAAO,OAAO,MAAM;AAC7C;AASA,IAAM,oBAAoB,CAAC,OAAO,OAAO,CAAC,aAAa,aAAa,MAAM,MAAM,MAAM;AACpF,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,UAAU;AACd,QAAM,SAAS,OAAO,UAAU,UAAU,sBAAsB,WAAW,GAAG,sBAAsB,WAAW,GAAG,sBAAsB,IAAI,CAAC;AAC7I,MAAI,QAAQ;AACV,WAAO,gBAAgB;AACvB,WAAO,SAAS;AAAA,EAClB;AACA,SAAO,gBAAgB,OAAO,OAAO,MAAM;AAC7C;AAMA,IAAM,eAAe,CAAC,OAAO,OAAO,CAAC,aAAa,aAAa,MAAM,MAAM;AACzE,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,UAAU;AACd,QAAM,eAAe,sBAAsB,WAAW;AACtD,QAAM,eAAe,sBAAsB,WAAW;AACtD,QAAM,SAAS,OAAO,UAAU,KAAK,QAAQ,cAAc,YAAY;AACvE,SAAO,gBAAgB,OAAO,OAAO,MAAM;AAC7C;AAMA,IAAM,iBAAiB,CAAC,OAAO,OAAO,CAAC,aAAa,aAAa,YAAY,WAAW,OAAO,MAAM;AACnG,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,UAAU;AACd,QAAM,eAAe,sBAAsB,WAAW;AACtD,QAAM,eAAe,sBAAsB,WAAW;AACtD,QAAM,SAAS,OAAO,UAAU,OAAO,YAAY,WAAW,SAAS,cAAc,YAAY;AACjG,SAAO,gBAAgB,OAAO,OAAO,MAAM;AAC7C;AAkCA,IAAM,oBAAoB,CAAC,aAAa,aAAa,QAAQ,WAAW,KAAK,OAAO,YAAY,SAAY,QAAQ,OAAO,IAAI;AAC/H,IAAM,UAAU,YAAU,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,UAAU,MAAM,KAAK,OAAO,CAAC;", "names": ["r", "React", "index"]}