{"version": 3, "sources": ["../../fflate/esm/browser.js"], "sourcesContent": ["// DEFLATE is a complex format; to read this code, you should probably check the RFC first:\n// https://tools.ietf.org/html/rfc1951\n// You may also wish to take a look at the guide I made about this program:\n// https://gist.github.com/101arrowz/253f31eb5abc3d9275ab943003ffecad\n// Some of the following code is similar to that of UZIP.js:\n// https://github.com/photopea/UZIP.js\n// However, the vast majority of the codebase has diverged from UZIP.js to increase performance and reduce bundle size.\n// Sometimes 0 will appear where -1 would be more appropriate. This is because using a uint\n// is better for memory in most engines (I *think*).\nvar ch2 = {};\nvar wk = (function (c, id, msg, transfer, cb) {\n    var w = new Worker(ch2[id] || (ch2[id] = URL.createObjectURL(new Blob([c], { type: 'text/javascript' }))));\n    w.onerror = function (e) { return cb(e.error, null); };\n    w.onmessage = function (e) { return cb(null, e.data); };\n    w.postMessage(msg, transfer);\n    return w;\n});\n\n// aliases for shorter compressed code (most minifers don't do this)\nvar u8 = Uint8Array, u16 = Uint16Array, u32 = Uint32Array;\n// fixed length extra bits\nvar fleb = new u8([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, /* unused */ 0, 0, /* impossible */ 0]);\n// fixed distance extra bits\n// see fleb note\nvar fdeb = new u8([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, /* unused */ 0, 0]);\n// code length index map\nvar clim = new u8([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]);\n// get base, reverse index map from extra bits\nvar freb = function (eb, start) {\n    var b = new u16(31);\n    for (var i = 0; i < 31; ++i) {\n        b[i] = start += 1 << eb[i - 1];\n    }\n    // numbers here are at max 18 bits\n    var r = new u32(b[30]);\n    for (var i = 1; i < 30; ++i) {\n        for (var j = b[i]; j < b[i + 1]; ++j) {\n            r[j] = ((j - b[i]) << 5) | i;\n        }\n    }\n    return [b, r];\n};\nvar _a = freb(fleb, 2), fl = _a[0], revfl = _a[1];\n// we can ignore the fact that the other numbers are wrong; they never happen anyway\nfl[28] = 258, revfl[258] = 28;\nvar _b = freb(fdeb, 0), fd = _b[0], revfd = _b[1];\n// map of value to reverse (assuming 16 bits)\nvar rev = new u16(32768);\nfor (var i = 0; i < 32768; ++i) {\n    // reverse table algorithm from SO\n    var x = ((i & 0xAAAA) >>> 1) | ((i & 0x5555) << 1);\n    x = ((x & 0xCCCC) >>> 2) | ((x & 0x3333) << 2);\n    x = ((x & 0xF0F0) >>> 4) | ((x & 0x0F0F) << 4);\n    rev[i] = (((x & 0xFF00) >>> 8) | ((x & 0x00FF) << 8)) >>> 1;\n}\n// create huffman tree from u8 \"map\": index -> code length for code index\n// mb (max bits) must be at most 15\n// TODO: optimize/split up?\nvar hMap = (function (cd, mb, r) {\n    var s = cd.length;\n    // index\n    var i = 0;\n    // u16 \"map\": index -> # of codes with bit length = index\n    var l = new u16(mb);\n    // length of cd must be 288 (total # of codes)\n    for (; i < s; ++i)\n        ++l[cd[i] - 1];\n    // u16 \"map\": index -> minimum code for bit length = index\n    var le = new u16(mb);\n    for (i = 0; i < mb; ++i) {\n        le[i] = (le[i - 1] + l[i - 1]) << 1;\n    }\n    var co;\n    if (r) {\n        // u16 \"map\": index -> number of actual bits, symbol for code\n        co = new u16(1 << mb);\n        // bits to remove for reverser\n        var rvb = 15 - mb;\n        for (i = 0; i < s; ++i) {\n            // ignore 0 lengths\n            if (cd[i]) {\n                // num encoding both symbol and bits read\n                var sv = (i << 4) | cd[i];\n                // free bits\n                var r_1 = mb - cd[i];\n                // start value\n                var v = le[cd[i] - 1]++ << r_1;\n                // m is end value\n                for (var m = v | ((1 << r_1) - 1); v <= m; ++v) {\n                    // every 16 bit value starting with the code yields the same result\n                    co[rev[v] >>> rvb] = sv;\n                }\n            }\n        }\n    }\n    else {\n        co = new u16(s);\n        for (i = 0; i < s; ++i) {\n            if (cd[i]) {\n                co[i] = rev[le[cd[i] - 1]++] >>> (15 - cd[i]);\n            }\n        }\n    }\n    return co;\n});\n// fixed length tree\nvar flt = new u8(288);\nfor (var i = 0; i < 144; ++i)\n    flt[i] = 8;\nfor (var i = 144; i < 256; ++i)\n    flt[i] = 9;\nfor (var i = 256; i < 280; ++i)\n    flt[i] = 7;\nfor (var i = 280; i < 288; ++i)\n    flt[i] = 8;\n// fixed distance tree\nvar fdt = new u8(32);\nfor (var i = 0; i < 32; ++i)\n    fdt[i] = 5;\n// fixed length map\nvar flm = /*#__PURE__*/ hMap(flt, 9, 0), flrm = /*#__PURE__*/ hMap(flt, 9, 1);\n// fixed distance map\nvar fdm = /*#__PURE__*/ hMap(fdt, 5, 0), fdrm = /*#__PURE__*/ hMap(fdt, 5, 1);\n// find max of array\nvar max = function (a) {\n    var m = a[0];\n    for (var i = 1; i < a.length; ++i) {\n        if (a[i] > m)\n            m = a[i];\n    }\n    return m;\n};\n// read d, starting at bit p and mask with m\nvar bits = function (d, p, m) {\n    var o = (p / 8) | 0;\n    return ((d[o] | (d[o + 1] << 8)) >> (p & 7)) & m;\n};\n// read d, starting at bit p continuing for at least 16 bits\nvar bits16 = function (d, p) {\n    var o = (p / 8) | 0;\n    return ((d[o] | (d[o + 1] << 8) | (d[o + 2] << 16)) >> (p & 7));\n};\n// get end of byte\nvar shft = function (p) { return ((p / 8) | 0) + (p & 7 && 1); };\n// typed array slice - allows garbage collector to free original reference,\n// while being more compatible than .slice\nvar slc = function (v, s, e) {\n    if (s == null || s < 0)\n        s = 0;\n    if (e == null || e > v.length)\n        e = v.length;\n    // can't use .constructor in case user-supplied\n    var n = new (v instanceof u16 ? u16 : v instanceof u32 ? u32 : u8)(e - s);\n    n.set(v.subarray(s, e));\n    return n;\n};\n// expands raw DEFLATE data\nvar inflt = function (dat, buf, st) {\n    // source length\n    var sl = dat.length;\n    if (!sl || (st && !st.l && sl < 5))\n        return buf || new u8(0);\n    // have to estimate size\n    var noBuf = !buf || st;\n    // no state\n    var noSt = !st || st.i;\n    if (!st)\n        st = {};\n    // Assumes roughly 33% compression ratio average\n    if (!buf)\n        buf = new u8(sl * 3);\n    // ensure buffer can fit at least l elements\n    var cbuf = function (l) {\n        var bl = buf.length;\n        // need to increase size to fit\n        if (l > bl) {\n            // Double or set to necessary, whichever is greater\n            var nbuf = new u8(Math.max(bl * 2, l));\n            nbuf.set(buf);\n            buf = nbuf;\n        }\n    };\n    //  last chunk         bitpos           bytes\n    var final = st.f || 0, pos = st.p || 0, bt = st.b || 0, lm = st.l, dm = st.d, lbt = st.m, dbt = st.n;\n    // total bits\n    var tbts = sl * 8;\n    do {\n        if (!lm) {\n            // BFINAL - this is only 1 when last chunk is next\n            st.f = final = bits(dat, pos, 1);\n            // type: 0 = no compression, 1 = fixed huffman, 2 = dynamic huffman\n            var type = bits(dat, pos + 1, 3);\n            pos += 3;\n            if (!type) {\n                // go to end of byte boundary\n                var s = shft(pos) + 4, l = dat[s - 4] | (dat[s - 3] << 8), t = s + l;\n                if (t > sl) {\n                    if (noSt)\n                        throw 'unexpected EOF';\n                    break;\n                }\n                // ensure size\n                if (noBuf)\n                    cbuf(bt + l);\n                // Copy over uncompressed data\n                buf.set(dat.subarray(s, t), bt);\n                // Get new bitpos, update byte count\n                st.b = bt += l, st.p = pos = t * 8;\n                continue;\n            }\n            else if (type == 1)\n                lm = flrm, dm = fdrm, lbt = 9, dbt = 5;\n            else if (type == 2) {\n                //  literal                            lengths\n                var hLit = bits(dat, pos, 31) + 257, hcLen = bits(dat, pos + 10, 15) + 4;\n                var tl = hLit + bits(dat, pos + 5, 31) + 1;\n                pos += 14;\n                // length+distance tree\n                var ldt = new u8(tl);\n                // code length tree\n                var clt = new u8(19);\n                for (var i = 0; i < hcLen; ++i) {\n                    // use index map to get real code\n                    clt[clim[i]] = bits(dat, pos + i * 3, 7);\n                }\n                pos += hcLen * 3;\n                // code lengths bits\n                var clb = max(clt), clbmsk = (1 << clb) - 1;\n                // code lengths map\n                var clm = hMap(clt, clb, 1);\n                for (var i = 0; i < tl;) {\n                    var r = clm[bits(dat, pos, clbmsk)];\n                    // bits read\n                    pos += r & 15;\n                    // symbol\n                    var s = r >>> 4;\n                    // code length to copy\n                    if (s < 16) {\n                        ldt[i++] = s;\n                    }\n                    else {\n                        //  copy   count\n                        var c = 0, n = 0;\n                        if (s == 16)\n                            n = 3 + bits(dat, pos, 3), pos += 2, c = ldt[i - 1];\n                        else if (s == 17)\n                            n = 3 + bits(dat, pos, 7), pos += 3;\n                        else if (s == 18)\n                            n = 11 + bits(dat, pos, 127), pos += 7;\n                        while (n--)\n                            ldt[i++] = c;\n                    }\n                }\n                //    length tree                 distance tree\n                var lt = ldt.subarray(0, hLit), dt = ldt.subarray(hLit);\n                // max length bits\n                lbt = max(lt);\n                // max dist bits\n                dbt = max(dt);\n                lm = hMap(lt, lbt, 1);\n                dm = hMap(dt, dbt, 1);\n            }\n            else\n                throw 'invalid block type';\n            if (pos > tbts) {\n                if (noSt)\n                    throw 'unexpected EOF';\n                break;\n            }\n        }\n        // Make sure the buffer can hold this + the largest possible addition\n        // Maximum chunk size (practically, theoretically infinite) is 2^17;\n        if (noBuf)\n            cbuf(bt + 131072);\n        var lms = (1 << lbt) - 1, dms = (1 << dbt) - 1;\n        var lpos = pos;\n        for (;; lpos = pos) {\n            // bits read, code\n            var c = lm[bits16(dat, pos) & lms], sym = c >>> 4;\n            pos += c & 15;\n            if (pos > tbts) {\n                if (noSt)\n                    throw 'unexpected EOF';\n                break;\n            }\n            if (!c)\n                throw 'invalid length/literal';\n            if (sym < 256)\n                buf[bt++] = sym;\n            else if (sym == 256) {\n                lpos = pos, lm = null;\n                break;\n            }\n            else {\n                var add = sym - 254;\n                // no extra bits needed if less\n                if (sym > 264) {\n                    // index\n                    var i = sym - 257, b = fleb[i];\n                    add = bits(dat, pos, (1 << b) - 1) + fl[i];\n                    pos += b;\n                }\n                // dist\n                var d = dm[bits16(dat, pos) & dms], dsym = d >>> 4;\n                if (!d)\n                    throw 'invalid distance';\n                pos += d & 15;\n                var dt = fd[dsym];\n                if (dsym > 3) {\n                    var b = fdeb[dsym];\n                    dt += bits16(dat, pos) & ((1 << b) - 1), pos += b;\n                }\n                if (pos > tbts) {\n                    if (noSt)\n                        throw 'unexpected EOF';\n                    break;\n                }\n                if (noBuf)\n                    cbuf(bt + 131072);\n                var end = bt + add;\n                for (; bt < end; bt += 4) {\n                    buf[bt] = buf[bt - dt];\n                    buf[bt + 1] = buf[bt + 1 - dt];\n                    buf[bt + 2] = buf[bt + 2 - dt];\n                    buf[bt + 3] = buf[bt + 3 - dt];\n                }\n                bt = end;\n            }\n        }\n        st.l = lm, st.p = lpos, st.b = bt;\n        if (lm)\n            final = 1, st.m = lbt, st.d = dm, st.n = dbt;\n    } while (!final);\n    return bt == buf.length ? buf : slc(buf, 0, bt);\n};\n// starting at p, write the minimum number of bits that can hold v to d\nvar wbits = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) | 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n};\n// starting at p, write the minimum number of bits (>8) that can hold v to d\nvar wbits16 = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) | 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n    d[o + 2] |= v >>> 16;\n};\n// creates code lengths from a frequency table\nvar hTree = function (d, mb) {\n    // Need extra info to make a tree\n    var t = [];\n    for (var i = 0; i < d.length; ++i) {\n        if (d[i])\n            t.push({ s: i, f: d[i] });\n    }\n    var s = t.length;\n    var t2 = t.slice();\n    if (!s)\n        return [et, 0];\n    if (s == 1) {\n        var v = new u8(t[0].s + 1);\n        v[t[0].s] = 1;\n        return [v, 1];\n    }\n    t.sort(function (a, b) { return a.f - b.f; });\n    // after i2 reaches last ind, will be stopped\n    // freq must be greater than largest possible number of symbols\n    t.push({ s: -1, f: 25001 });\n    var l = t[0], r = t[1], i0 = 0, i1 = 1, i2 = 2;\n    t[0] = { s: -1, f: l.f + r.f, l: l, r: r };\n    // efficient algorithm from UZIP.js\n    // i0 is lookbehind, i2 is lookahead - after processing two low-freq\n    // symbols that combined have high freq, will start processing i2 (high-freq,\n    // non-composite) symbols instead\n    // see https://reddit.com/r/photopea/comments/ikekht/uzipjs_questions/\n    while (i1 != s - 1) {\n        l = t[t[i0].f < t[i2].f ? i0++ : i2++];\n        r = t[i0 != i1 && t[i0].f < t[i2].f ? i0++ : i2++];\n        t[i1++] = { s: -1, f: l.f + r.f, l: l, r: r };\n    }\n    var maxSym = t2[0].s;\n    for (var i = 1; i < s; ++i) {\n        if (t2[i].s > maxSym)\n            maxSym = t2[i].s;\n    }\n    // code lengths\n    var tr = new u16(maxSym + 1);\n    // max bits in tree\n    var mbt = ln(t[i1 - 1], tr, 0);\n    if (mbt > mb) {\n        // more algorithms from UZIP.js\n        // TODO: find out how this code works (debt)\n        //  ind    debt\n        var i = 0, dt = 0;\n        //    left            cost\n        var lft = mbt - mb, cst = 1 << lft;\n        t2.sort(function (a, b) { return tr[b.s] - tr[a.s] || a.f - b.f; });\n        for (; i < s; ++i) {\n            var i2_1 = t2[i].s;\n            if (tr[i2_1] > mb) {\n                dt += cst - (1 << (mbt - tr[i2_1]));\n                tr[i2_1] = mb;\n            }\n            else\n                break;\n        }\n        dt >>>= lft;\n        while (dt > 0) {\n            var i2_2 = t2[i].s;\n            if (tr[i2_2] < mb)\n                dt -= 1 << (mb - tr[i2_2]++ - 1);\n            else\n                ++i;\n        }\n        for (; i >= 0 && dt; --i) {\n            var i2_3 = t2[i].s;\n            if (tr[i2_3] == mb) {\n                --tr[i2_3];\n                ++dt;\n            }\n        }\n        mbt = mb;\n    }\n    return [new u8(tr), mbt];\n};\n// get the max length and assign length codes\nvar ln = function (n, l, d) {\n    return n.s == -1\n        ? Math.max(ln(n.l, l, d + 1), ln(n.r, l, d + 1))\n        : (l[n.s] = d);\n};\n// length codes generation\nvar lc = function (c) {\n    var s = c.length;\n    // Note that the semicolon was intentional\n    while (s && !c[--s])\n        ;\n    var cl = new u16(++s);\n    //  ind      num         streak\n    var cli = 0, cln = c[0], cls = 1;\n    var w = function (v) { cl[cli++] = v; };\n    for (var i = 1; i <= s; ++i) {\n        if (c[i] == cln && i != s)\n            ++cls;\n        else {\n            if (!cln && cls > 2) {\n                for (; cls > 138; cls -= 138)\n                    w(32754);\n                if (cls > 2) {\n                    w(cls > 10 ? ((cls - 11) << 5) | 28690 : ((cls - 3) << 5) | 12305);\n                    cls = 0;\n                }\n            }\n            else if (cls > 3) {\n                w(cln), --cls;\n                for (; cls > 6; cls -= 6)\n                    w(8304);\n                if (cls > 2)\n                    w(((cls - 3) << 5) | 8208), cls = 0;\n            }\n            while (cls--)\n                w(cln);\n            cls = 1;\n            cln = c[i];\n        }\n    }\n    return [cl.subarray(0, cli), s];\n};\n// calculate the length of output from tree, code lengths\nvar clen = function (cf, cl) {\n    var l = 0;\n    for (var i = 0; i < cl.length; ++i)\n        l += cf[i] * cl[i];\n    return l;\n};\n// writes a fixed block\n// returns the new bit pos\nvar wfblk = function (out, pos, dat) {\n    // no need to write 00 as type: TypedArray defaults to 0\n    var s = dat.length;\n    var o = shft(pos + 2);\n    out[o] = s & 255;\n    out[o + 1] = s >>> 8;\n    out[o + 2] = out[o] ^ 255;\n    out[o + 3] = out[o + 1] ^ 255;\n    for (var i = 0; i < s; ++i)\n        out[o + i + 4] = dat[i];\n    return (o + 4 + s) * 8;\n};\n// writes a block\nvar wblk = function (dat, out, final, syms, lf, df, eb, li, bs, bl, p) {\n    wbits(out, p++, final);\n    ++lf[256];\n    var _a = hTree(lf, 15), dlt = _a[0], mlb = _a[1];\n    var _b = hTree(df, 15), ddt = _b[0], mdb = _b[1];\n    var _c = lc(dlt), lclt = _c[0], nlc = _c[1];\n    var _d = lc(ddt), lcdt = _d[0], ndc = _d[1];\n    var lcfreq = new u16(19);\n    for (var i = 0; i < lclt.length; ++i)\n        lcfreq[lclt[i] & 31]++;\n    for (var i = 0; i < lcdt.length; ++i)\n        lcfreq[lcdt[i] & 31]++;\n    var _e = hTree(lcfreq, 7), lct = _e[0], mlcb = _e[1];\n    var nlcc = 19;\n    for (; nlcc > 4 && !lct[clim[nlcc - 1]]; --nlcc)\n        ;\n    var flen = (bl + 5) << 3;\n    var ftlen = clen(lf, flt) + clen(df, fdt) + eb;\n    var dtlen = clen(lf, dlt) + clen(df, ddt) + eb + 14 + 3 * nlcc + clen(lcfreq, lct) + (2 * lcfreq[16] + 3 * lcfreq[17] + 7 * lcfreq[18]);\n    if (flen <= ftlen && flen <= dtlen)\n        return wfblk(out, p, dat.subarray(bs, bs + bl));\n    var lm, ll, dm, dl;\n    wbits(out, p, 1 + (dtlen < ftlen)), p += 2;\n    if (dtlen < ftlen) {\n        lm = hMap(dlt, mlb, 0), ll = dlt, dm = hMap(ddt, mdb, 0), dl = ddt;\n        var llm = hMap(lct, mlcb, 0);\n        wbits(out, p, nlc - 257);\n        wbits(out, p + 5, ndc - 1);\n        wbits(out, p + 10, nlcc - 4);\n        p += 14;\n        for (var i = 0; i < nlcc; ++i)\n            wbits(out, p + 3 * i, lct[clim[i]]);\n        p += 3 * nlcc;\n        var lcts = [lclt, lcdt];\n        for (var it = 0; it < 2; ++it) {\n            var clct = lcts[it];\n            for (var i = 0; i < clct.length; ++i) {\n                var len = clct[i] & 31;\n                wbits(out, p, llm[len]), p += lct[len];\n                if (len > 15)\n                    wbits(out, p, (clct[i] >>> 5) & 127), p += clct[i] >>> 12;\n            }\n        }\n    }\n    else {\n        lm = flm, ll = flt, dm = fdm, dl = fdt;\n    }\n    for (var i = 0; i < li; ++i) {\n        if (syms[i] > 255) {\n            var len = (syms[i] >>> 18) & 31;\n            wbits16(out, p, lm[len + 257]), p += ll[len + 257];\n            if (len > 7)\n                wbits(out, p, (syms[i] >>> 23) & 31), p += fleb[len];\n            var dst = syms[i] & 31;\n            wbits16(out, p, dm[dst]), p += dl[dst];\n            if (dst > 3)\n                wbits16(out, p, (syms[i] >>> 5) & 8191), p += fdeb[dst];\n        }\n        else {\n            wbits16(out, p, lm[syms[i]]), p += ll[syms[i]];\n        }\n    }\n    wbits16(out, p, lm[256]);\n    return p + ll[256];\n};\n// deflate options (nice << 13) | chain\nvar deo = /*#__PURE__*/ new u32([65540, 131080, 131088, 131104, 262176, 1048704, 1048832, 2114560, 2117632]);\n// empty\nvar et = /*#__PURE__*/ new u8(0);\n// compresses data into a raw DEFLATE buffer\nvar dflt = function (dat, lvl, plvl, pre, post, lst) {\n    var s = dat.length;\n    var o = new u8(pre + s + 5 * (1 + Math.ceil(s / 7000)) + post);\n    // writing to this writes to the output buffer\n    var w = o.subarray(pre, o.length - post);\n    var pos = 0;\n    if (!lvl || s < 8) {\n        for (var i = 0; i <= s; i += 65535) {\n            // end\n            var e = i + 65535;\n            if (e < s) {\n                // write full block\n                pos = wfblk(w, pos, dat.subarray(i, e));\n            }\n            else {\n                // write final block\n                w[i] = lst;\n                pos = wfblk(w, pos, dat.subarray(i, s));\n            }\n        }\n    }\n    else {\n        var opt = deo[lvl - 1];\n        var n = opt >>> 13, c = opt & 8191;\n        var msk_1 = (1 << plvl) - 1;\n        //    prev 2-byte val map    curr 2-byte val map\n        var prev = new u16(32768), head = new u16(msk_1 + 1);\n        var bs1_1 = Math.ceil(plvl / 3), bs2_1 = 2 * bs1_1;\n        var hsh = function (i) { return (dat[i] ^ (dat[i + 1] << bs1_1) ^ (dat[i + 2] << bs2_1)) & msk_1; };\n        // 24576 is an arbitrary number of maximum symbols per block\n        // 424 buffer for last block\n        var syms = new u32(25000);\n        // length/literal freq   distance freq\n        var lf = new u16(288), df = new u16(32);\n        //  l/lcnt  exbits  index  l/lind  waitdx  bitpos\n        var lc_1 = 0, eb = 0, i = 0, li = 0, wi = 0, bs = 0;\n        for (; i < s; ++i) {\n            // hash value\n            // deopt when i > s - 3 - at end, deopt acceptable\n            var hv = hsh(i);\n            // index mod 32768    previous index mod\n            var imod = i & 32767, pimod = head[hv];\n            prev[imod] = pimod;\n            head[hv] = imod;\n            // We always should modify head and prev, but only add symbols if\n            // this data is not yet processed (\"wait\" for wait index)\n            if (wi <= i) {\n                // bytes remaining\n                var rem = s - i;\n                if ((lc_1 > 7000 || li > 24576) && rem > 423) {\n                    pos = wblk(dat, w, 0, syms, lf, df, eb, li, bs, i - bs, pos);\n                    li = lc_1 = eb = 0, bs = i;\n                    for (var j = 0; j < 286; ++j)\n                        lf[j] = 0;\n                    for (var j = 0; j < 30; ++j)\n                        df[j] = 0;\n                }\n                //  len    dist   chain\n                var l = 2, d = 0, ch_1 = c, dif = (imod - pimod) & 32767;\n                if (rem > 2 && hv == hsh(i - dif)) {\n                    var maxn = Math.min(n, rem) - 1;\n                    var maxd = Math.min(32767, i);\n                    // max possible length\n                    // not capped at dif because decompressors implement \"rolling\" index population\n                    var ml = Math.min(258, rem);\n                    while (dif <= maxd && --ch_1 && imod != pimod) {\n                        if (dat[i + l] == dat[i + l - dif]) {\n                            var nl = 0;\n                            for (; nl < ml && dat[i + nl] == dat[i + nl - dif]; ++nl)\n                                ;\n                            if (nl > l) {\n                                l = nl, d = dif;\n                                // break out early when we reach \"nice\" (we are satisfied enough)\n                                if (nl > maxn)\n                                    break;\n                                // now, find the rarest 2-byte sequence within this\n                                // length of literals and search for that instead.\n                                // Much faster than just using the start\n                                var mmd = Math.min(dif, nl - 2);\n                                var md = 0;\n                                for (var j = 0; j < mmd; ++j) {\n                                    var ti = (i - dif + j + 32768) & 32767;\n                                    var pti = prev[ti];\n                                    var cd = (ti - pti + 32768) & 32767;\n                                    if (cd > md)\n                                        md = cd, pimod = ti;\n                                }\n                            }\n                        }\n                        // check the previous match\n                        imod = pimod, pimod = prev[imod];\n                        dif += (imod - pimod + 32768) & 32767;\n                    }\n                }\n                // d will be nonzero only when a match was found\n                if (d) {\n                    // store both dist and len data in one Uint32\n                    // Make sure this is recognized as a len/dist with 28th bit (2^28)\n                    syms[li++] = 268435456 | (revfl[l] << 18) | revfd[d];\n                    var lin = revfl[l] & 31, din = revfd[d] & 31;\n                    eb += fleb[lin] + fdeb[din];\n                    ++lf[257 + lin];\n                    ++df[din];\n                    wi = i + l;\n                    ++lc_1;\n                }\n                else {\n                    syms[li++] = dat[i];\n                    ++lf[dat[i]];\n                }\n            }\n        }\n        pos = wblk(dat, w, lst, syms, lf, df, eb, li, bs, i - bs, pos);\n        // this is the easiest way to avoid needing to maintain state\n        if (!lst && pos & 7)\n            pos = wfblk(w, pos + 1, et);\n    }\n    return slc(o, 0, pre + shft(pos) + post);\n};\n// CRC32 table\nvar crct = /*#__PURE__*/ (function () {\n    var t = new Int32Array(256);\n    for (var i = 0; i < 256; ++i) {\n        var c = i, k = 9;\n        while (--k)\n            c = ((c & 1) && -306674912) ^ (c >>> 1);\n        t[i] = c;\n    }\n    return t;\n})();\n// CRC32\nvar crc = function () {\n    var c = -1;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var cr = c;\n            for (var i = 0; i < d.length; ++i)\n                cr = crct[(cr & 255) ^ d[i]] ^ (cr >>> 8);\n            c = cr;\n        },\n        d: function () { return ~c; }\n    };\n};\n// Alder32\nvar adler = function () {\n    var a = 1, b = 0;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var n = a, m = b;\n            var l = d.length;\n            for (var i = 0; i != l;) {\n                var e = Math.min(i + 2655, l);\n                for (; i < e; ++i)\n                    m += n += d[i];\n                n = (n & 65535) + 15 * (n >> 16), m = (m & 65535) + 15 * (m >> 16);\n            }\n            a = n, b = m;\n        },\n        d: function () {\n            a %= 65521, b %= 65521;\n            return (a & 255) << 24 | (a >>> 8) << 16 | (b & 255) << 8 | (b >>> 8);\n        }\n    };\n};\n;\n// deflate with opts\nvar dopt = function (dat, opt, pre, post, st) {\n    return dflt(dat, opt.level == null ? 6 : opt.level, opt.mem == null ? Math.ceil(Math.max(8, Math.min(13, Math.log(dat.length))) * 1.5) : (12 + opt.mem), pre, post, !st);\n};\n// Walmart object spread\nvar mrg = function (a, b) {\n    var o = {};\n    for (var k in a)\n        o[k] = a[k];\n    for (var k in b)\n        o[k] = b[k];\n    return o;\n};\n// worker clone\n// This is possibly the craziest part of the entire codebase, despite how simple it may seem.\n// The only parameter to this function is a closure that returns an array of variables outside of the function scope.\n// We're going to try to figure out the variable names used in the closure as strings because that is crucial for workerization.\n// We will return an object mapping of true variable name to value (basically, the current scope as a JS object).\n// The reason we can't just use the original variable names is minifiers mangling the toplevel scope.\n// This took me three weeks to figure out how to do.\nvar wcln = function (fn, fnStr, td) {\n    var dt = fn();\n    var st = fn.toString();\n    var ks = st.slice(st.indexOf('[') + 1, st.lastIndexOf(']')).replace(/ /g, '').split(',');\n    for (var i = 0; i < dt.length; ++i) {\n        var v = dt[i], k = ks[i];\n        if (typeof v == 'function') {\n            fnStr += ';' + k + '=';\n            var st_1 = v.toString();\n            if (v.prototype) {\n                // for global objects\n                if (st_1.indexOf('[native code]') != -1) {\n                    var spInd = st_1.indexOf(' ', 8) + 1;\n                    fnStr += st_1.slice(spInd, st_1.indexOf('(', spInd));\n                }\n                else {\n                    fnStr += st_1;\n                    for (var t in v.prototype)\n                        fnStr += ';' + k + '.prototype.' + t + '=' + v.prototype[t].toString();\n                }\n            }\n            else\n                fnStr += st_1;\n        }\n        else\n            td[k] = v;\n    }\n    return [fnStr, td];\n};\nvar ch = [];\n// clone bufs\nvar cbfs = function (v) {\n    var tl = [];\n    for (var k in v) {\n        if (v[k] instanceof u8 || v[k] instanceof u16 || v[k] instanceof u32)\n            tl.push((v[k] = new v[k].constructor(v[k])).buffer);\n    }\n    return tl;\n};\n// use a worker to execute code\nvar wrkr = function (fns, init, id, cb) {\n    var _a;\n    if (!ch[id]) {\n        var fnStr = '', td_1 = {}, m = fns.length - 1;\n        for (var i = 0; i < m; ++i)\n            _a = wcln(fns[i], fnStr, td_1), fnStr = _a[0], td_1 = _a[1];\n        ch[id] = wcln(fns[m], fnStr, td_1);\n    }\n    var td = mrg({}, ch[id][1]);\n    return wk(ch[id][0] + ';onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage=' + init.toString() + '}', id, td, cbfs(td), cb);\n};\n// base async inflate fn\nvar bInflt = function () { return [u8, u16, u32, fleb, fdeb, clim, fl, fd, flrm, fdrm, rev, hMap, max, bits, bits16, shft, slc, inflt, inflateSync, pbf, gu8]; };\nvar bDflt = function () { return [u8, u16, u32, fleb, fdeb, clim, revfl, revfd, flm, flt, fdm, fdt, rev, deo, et, hMap, wbits, wbits16, hTree, ln, lc, clen, wfblk, wblk, shft, slc, dflt, dopt, deflateSync, pbf]; };\n// gzip extra\nvar gze = function () { return [gzh, gzhl, wbytes, crc, crct]; };\n// gunzip extra\nvar guze = function () { return [gzs, gzl]; };\n// zlib extra\nvar zle = function () { return [zlh, wbytes, adler]; };\n// unzlib extra\nvar zule = function () { return [zlv]; };\n// post buf\nvar pbf = function (msg) { return postMessage(msg, [msg.buffer]); };\n// get u8\nvar gu8 = function (o) { return o && o.size && new u8(o.size); };\n// async helper\nvar cbify = function (dat, opts, fns, init, id, cb) {\n    var w = wrkr(fns, init, id, function (err, dat) {\n        w.terminate();\n        cb(err, dat);\n    });\n    w.postMessage([dat, opts], opts.consume ? [dat.buffer] : []);\n    return function () { w.terminate(); };\n};\n// auto stream\nvar astrm = function (strm) {\n    strm.ondata = function (dat, final) { return postMessage([dat, final], [dat.buffer]); };\n    return function (ev) { return strm.push(ev.data[0], ev.data[1]); };\n};\n// async stream attach\nvar astrmify = function (fns, strm, opts, init, id) {\n    var t;\n    var w = wrkr(fns, init, id, function (err, dat) {\n        if (err)\n            w.terminate(), strm.ondata.call(strm, err);\n        else {\n            if (dat[1])\n                w.terminate();\n            strm.ondata.call(strm, err, dat[0], dat[1]);\n        }\n    });\n    w.postMessage(opts);\n    strm.push = function (d, f) {\n        if (t)\n            throw 'stream finished';\n        if (!strm.ondata)\n            throw 'no stream handler';\n        w.postMessage([d, t = f], [d.buffer]);\n    };\n    strm.terminate = function () { w.terminate(); };\n};\n// read 2 bytes\nvar b2 = function (d, b) { return d[b] | (d[b + 1] << 8); };\n// read 4 bytes\nvar b4 = function (d, b) { return (d[b] | (d[b + 1] << 8) | (d[b + 2] << 16) | (d[b + 3] << 24)) >>> 0; };\nvar b8 = function (d, b) { return b4(d, b) + (b4(d, b + 4) * 4294967296); };\n// write bytes\nvar wbytes = function (d, b, v) {\n    for (; v; ++b)\n        d[b] = v, v >>>= 8;\n};\n// gzip header\nvar gzh = function (c, o) {\n    var fn = o.filename;\n    c[0] = 31, c[1] = 139, c[2] = 8, c[8] = o.level < 2 ? 4 : o.level == 9 ? 2 : 0, c[9] = 3; // assume Unix\n    if (o.mtime != 0)\n        wbytes(c, 4, Math.floor(new Date(o.mtime || Date.now()) / 1000));\n    if (fn) {\n        c[3] = 8;\n        for (var i = 0; i <= fn.length; ++i)\n            c[i + 10] = fn.charCodeAt(i);\n    }\n};\n// gzip footer: -8 to -4 = CRC, -4 to -0 is length\n// gzip start\nvar gzs = function (d) {\n    if (d[0] != 31 || d[1] != 139 || d[2] != 8)\n        throw 'invalid gzip data';\n    var flg = d[3];\n    var st = 10;\n    if (flg & 4)\n        st += d[10] | (d[11] << 8) + 2;\n    for (var zs = (flg >> 3 & 1) + (flg >> 4 & 1); zs > 0; zs -= !d[st++])\n        ;\n    return st + (flg & 2);\n};\n// gzip length\nvar gzl = function (d) {\n    var l = d.length;\n    return ((d[l - 4] | d[l - 3] << 8 | d[l - 2] << 16) | (d[l - 1] << 24)) >>> 0;\n};\n// gzip header length\nvar gzhl = function (o) { return 10 + ((o.filename && (o.filename.length + 1)) || 0); };\n// zlib header\nvar zlh = function (c, o) {\n    var lv = o.level, fl = lv == 0 ? 0 : lv < 6 ? 1 : lv == 9 ? 3 : 2;\n    c[0] = 120, c[1] = (fl << 6) | (fl ? (32 - 2 * fl) : 1);\n};\n// zlib valid\nvar zlv = function (d) {\n    if ((d[0] & 15) != 8 || (d[0] >>> 4) > 7 || ((d[0] << 8 | d[1]) % 31))\n        throw 'invalid zlib data';\n    if (d[1] & 32)\n        throw 'invalid zlib data: preset dictionaries not supported';\n};\nfunction AsyncCmpStrm(opts, cb) {\n    if (!cb && typeof opts == 'function')\n        cb = opts, opts = {};\n    this.ondata = cb;\n    return opts;\n}\n// zlib footer: -4 to -0 is Adler32\n/**\n * Streaming DEFLATE compression\n */\nvar Deflate = /*#__PURE__*/ (function () {\n    function Deflate(opts, cb) {\n        if (!cb && typeof opts == 'function')\n            cb = opts, opts = {};\n        this.ondata = cb;\n        this.o = opts || {};\n    }\n    Deflate.prototype.p = function (c, f) {\n        this.ondata(dopt(c, this.o, 0, 0, !f), f);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Deflate.prototype.push = function (chunk, final) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        this.d = final;\n        this.p(chunk, final || false);\n    };\n    return Deflate;\n}());\nexport { Deflate };\n/**\n * Asynchronous streaming DEFLATE compression\n */\nvar AsyncDeflate = /*#__PURE__*/ (function () {\n    function AsyncDeflate(opts, cb) {\n        astrmify([\n            bDflt,\n            function () { return [astrm, Deflate]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Deflate(ev.data);\n            onmessage = astrm(strm);\n        }, 6);\n    }\n    return AsyncDeflate;\n}());\nexport { AsyncDeflate };\nexport function deflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n    ], function (ev) { return pbf(deflateSync(ev.data[0], ev.data[1])); }, 0, cb);\n}\n/**\n * Compresses data with DEFLATE without any wrapper\n * @param data The data to compress\n * @param opts The compression options\n * @returns The deflated version of the data\n */\nexport function deflateSync(data, opts) {\n    return dopt(data, opts || {}, 0, 0);\n}\n/**\n * Streaming DEFLATE decompression\n */\nvar Inflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an inflation stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Inflate(cb) {\n        this.s = {};\n        this.p = new u8(0);\n        this.ondata = cb;\n    }\n    Inflate.prototype.e = function (c) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        var l = this.p.length;\n        var n = new u8(l + c.length);\n        n.set(this.p), n.set(c, l), this.p = n;\n    };\n    Inflate.prototype.c = function (final) {\n        this.d = this.s.i = final || false;\n        var bts = this.s.b;\n        var dt = inflt(this.p, this.o, this.s);\n        this.ondata(slc(dt, bts, this.s.b), this.d);\n        this.o = slc(dt, this.s.b - 32768), this.s.b = this.o.length;\n        this.p = slc(this.p, (this.s.p / 8) | 0), this.s.p &= 7;\n    };\n    /**\n     * Pushes a chunk to be inflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the final chunk\n     */\n    Inflate.prototype.push = function (chunk, final) {\n        this.e(chunk), this.c(final);\n    };\n    return Inflate;\n}());\nexport { Inflate };\n/**\n * Asynchronous streaming DEFLATE decompression\n */\nvar AsyncInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous inflation stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncInflate(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            function () { return [astrm, Inflate]; }\n        ], this, 0, function () {\n            var strm = new Inflate();\n            onmessage = astrm(strm);\n        }, 7);\n    }\n    return AsyncInflate;\n}());\nexport { AsyncInflate };\nexport function inflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt\n    ], function (ev) { return pbf(inflateSync(ev.data[0], gu8(ev.data[1]))); }, 1, cb);\n}\n/**\n * Expands DEFLATE data with no wrapper\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function inflateSync(data, out) {\n    return inflt(data, out);\n}\n// before you yell at me for not just using extends, my reason is that TS inheritance is hard to workerize.\n/**\n * Streaming GZIP compression\n */\nvar Gzip = /*#__PURE__*/ (function () {\n    function Gzip(opts, cb) {\n        this.c = crc();\n        this.l = 0;\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be GZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gzip.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Gzip.prototype.p = function (c, f) {\n        this.c.p(c);\n        this.l += c.length;\n        var raw = dopt(c, this.o, this.v && gzhl(this.o), f && 8, !f);\n        if (this.v)\n            gzh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 8, this.c.d()), wbytes(raw, raw.length - 4, this.l);\n        this.ondata(raw, f);\n    };\n    return Gzip;\n}());\nexport { Gzip };\n/**\n * Asynchronous streaming GZIP compression\n */\nvar AsyncGzip = /*#__PURE__*/ (function () {\n    function AsyncGzip(opts, cb) {\n        astrmify([\n            bDflt,\n            gze,\n            function () { return [astrm, Deflate, Gzip]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Gzip(ev.data);\n            onmessage = astrm(strm);\n        }, 8);\n    }\n    return AsyncGzip;\n}());\nexport { AsyncGzip };\nexport function gzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        gze,\n        function () { return [gzipSync]; }\n    ], function (ev) { return pbf(gzipSync(ev.data[0], ev.data[1])); }, 2, cb);\n}\n/**\n * Compresses data with GZIP\n * @param data The data to compress\n * @param opts The compression options\n * @returns The gzipped version of the data\n */\nexport function gzipSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var c = crc(), l = data.length;\n    c.p(data);\n    var d = dopt(data, opts, gzhl(opts), 8), s = d.length;\n    return gzh(d, opts), wbytes(d, s - 8, c.d()), wbytes(d, s - 4, l), d;\n}\n/**\n * Streaming GZIP decompression\n */\nvar Gunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates a GUNZIP stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Gunzip(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be GUNZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gunzip.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            var s = this.p.length > 3 ? gzs(this.p) : 4;\n            if (s >= this.p.length && !final)\n                return;\n            this.p = this.p.subarray(s), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 8)\n                throw 'invalid gzip stream';\n            this.p = this.p.subarray(0, -8);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Gunzip;\n}());\nexport { Gunzip };\n/**\n * Asynchronous streaming GZIP decompression\n */\nvar AsyncGunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous GUNZIP stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncGunzip(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            guze,\n            function () { return [astrm, Inflate, Gunzip]; }\n        ], this, 0, function () {\n            var strm = new Gunzip();\n            onmessage = astrm(strm);\n        }, 9);\n    }\n    return AsyncGunzip;\n}());\nexport { AsyncGunzip };\nexport function gunzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        guze,\n        function () { return [gunzipSync]; }\n    ], function (ev) { return pbf(gunzipSync(ev.data[0])); }, 3, cb);\n}\n/**\n * Expands GZIP data\n * @param data The data to decompress\n * @param out Where to write the data. GZIP already encodes the output size, so providing this doesn't save memory.\n * @returns The decompressed version of the data\n */\nexport function gunzipSync(data, out) {\n    return inflt(data.subarray(gzs(data), -8), out || new u8(gzl(data)));\n}\n/**\n * Streaming Zlib compression\n */\nvar Zlib = /*#__PURE__*/ (function () {\n    function Zlib(opts, cb) {\n        this.c = adler();\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be zlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Zlib.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Zlib.prototype.p = function (c, f) {\n        this.c.p(c);\n        var raw = dopt(c, this.o, this.v && 2, f && 4, !f);\n        if (this.v)\n            zlh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 4, this.c.d());\n        this.ondata(raw, f);\n    };\n    return Zlib;\n}());\nexport { Zlib };\n/**\n * Asynchronous streaming Zlib compression\n */\nvar AsyncZlib = /*#__PURE__*/ (function () {\n    function AsyncZlib(opts, cb) {\n        astrmify([\n            bDflt,\n            zle,\n            function () { return [astrm, Deflate, Zlib]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Zlib(ev.data);\n            onmessage = astrm(strm);\n        }, 10);\n    }\n    return AsyncZlib;\n}());\nexport { AsyncZlib };\nexport function zlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        zle,\n        function () { return [zlibSync]; }\n    ], function (ev) { return pbf(zlibSync(ev.data[0], ev.data[1])); }, 4, cb);\n}\n/**\n * Compress data with Zlib\n * @param data The data to compress\n * @param opts The compression options\n * @returns The zlib-compressed version of the data\n */\nexport function zlibSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var a = adler();\n    a.p(data);\n    var d = dopt(data, opts, 2, 4);\n    return zlh(d, opts), wbytes(d, d.length - 4, a.d()), d;\n}\n/**\n * Streaming Zlib decompression\n */\nvar Unzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates a Zlib decompression stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Unzlib(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be unzlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzlib.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            if (this.p.length < 2 && !final)\n                return;\n            this.p = this.p.subarray(2), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 4)\n                throw 'invalid zlib stream';\n            this.p = this.p.subarray(0, -4);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Unzlib;\n}());\nexport { Unzlib };\n/**\n * Asynchronous streaming Zlib decompression\n */\nvar AsyncUnzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous Zlib decompression stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncUnzlib(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            zule,\n            function () { return [astrm, Inflate, Unzlib]; }\n        ], this, 0, function () {\n            var strm = new Unzlib();\n            onmessage = astrm(strm);\n        }, 11);\n    }\n    return AsyncUnzlib;\n}());\nexport { AsyncUnzlib };\nexport function unzlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        zule,\n        function () { return [unzlibSync]; }\n    ], function (ev) { return pbf(unzlibSync(ev.data[0], gu8(ev.data[1]))); }, 5, cb);\n}\n/**\n * Expands Zlib data\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function unzlibSync(data, out) {\n    return inflt((zlv(data), data.subarray(2, -4)), out);\n}\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzip as compress, AsyncGzip as AsyncCompress };\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzipSync as compressSync, Gzip as Compress };\n/**\n * Streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar Decompress = /*#__PURE__*/ (function () {\n    /**\n     * Creates a decompression stream\n     * @param cb The callback to call whenever data is decompressed\n     */\n    function Decompress(cb) {\n        this.G = Gunzip;\n        this.I = Inflate;\n        this.Z = Unzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Decompress.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no stream handler';\n        if (!this.s) {\n            if (this.p && this.p.length) {\n                var n = new u8(this.p.length + chunk.length);\n                n.set(this.p), n.set(chunk, this.p.length);\n            }\n            else\n                this.p = chunk;\n            if (this.p.length > 2) {\n                var _this_1 = this;\n                var cb = function () { _this_1.ondata.apply(_this_1, arguments); };\n                this.s = (this.p[0] == 31 && this.p[1] == 139 && this.p[2] == 8)\n                    ? new this.G(cb)\n                    : ((this.p[0] & 15) != 8 || (this.p[0] >> 4) > 7 || ((this.p[0] << 8 | this.p[1]) % 31))\n                        ? new this.I(cb)\n                        : new this.Z(cb);\n                this.s.push(this.p, final);\n                this.p = null;\n            }\n        }\n        else\n            this.s.push(chunk, final);\n    };\n    return Decompress;\n}());\nexport { Decompress };\n/**\n * Asynchronous streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar AsyncDecompress = /*#__PURE__*/ (function () {\n    /**\n   * Creates an asynchronous decompression stream\n   * @param cb The callback to call whenever data is decompressed\n   */\n    function AsyncDecompress(cb) {\n        this.G = AsyncGunzip;\n        this.I = AsyncInflate;\n        this.Z = AsyncUnzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncDecompress.prototype.push = function (chunk, final) {\n        Decompress.prototype.push.call(this, chunk, final);\n    };\n    return AsyncDecompress;\n}());\nexport { AsyncDecompress };\nexport function decompress(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzip(data, opts, cb)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflate(data, opts, cb)\n            : unzlib(data, opts, cb);\n}\n/**\n * Expands compressed GZIP, Zlib, or raw DEFLATE data, automatically detecting the format\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function decompressSync(data, out) {\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzipSync(data, out)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflateSync(data, out)\n            : unzlibSync(data, out);\n}\n// flatten a directory structure\nvar fltn = function (d, p, t, o) {\n    for (var k in d) {\n        var val = d[k], n = p + k;\n        if (val instanceof u8)\n            t[n] = [val, o];\n        else if (Array.isArray(val))\n            t[n] = [val[0], mrg(o, val[1])];\n        else\n            fltn(val, n + '/', t, o);\n    }\n};\n// text encoder\nvar te = typeof TextEncoder != 'undefined' && /*#__PURE__*/ new TextEncoder();\n// text decoder\nvar td = typeof TextDecoder != 'undefined' && /*#__PURE__*/ new TextDecoder();\n// text decoder stream\nvar tds = 0;\ntry {\n    td.decode(et, { stream: true });\n    tds = 1;\n}\ncatch (e) { }\n// decode UTF8\nvar dutf8 = function (d) {\n    for (var r = '', i = 0;;) {\n        var c = d[i++];\n        var eb = (c > 127) + (c > 223) + (c > 239);\n        if (i + eb > d.length)\n            return [r, slc(d, i - 1)];\n        if (!eb)\n            r += String.fromCharCode(c);\n        else if (eb == 3) {\n            c = ((c & 15) << 18 | (d[i++] & 63) << 12 | (d[i++] & 63) << 6 | (d[i++] & 63)) - 65536,\n                r += String.fromCharCode(55296 | (c >> 10), 56320 | (c & 1023));\n        }\n        else if (eb & 1)\n            r += String.fromCharCode((c & 31) << 6 | (d[i++] & 63));\n        else\n            r += String.fromCharCode((c & 15) << 12 | (d[i++] & 63) << 6 | (d[i++] & 63));\n    }\n};\n/**\n * Streaming UTF-8 decoding\n */\nvar DecodeUTF8 = /*#__PURE__*/ (function () {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is decoded\n     */\n    function DecodeUTF8(cb) {\n        this.ondata = cb;\n        if (tds)\n            this.t = new TextDecoder();\n        else\n            this.p = et;\n    }\n    /**\n     * Pushes a chunk to be decoded from UTF-8 binary\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    DecodeUTF8.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback';\n        final = !!final;\n        if (this.t) {\n            this.ondata(this.t.decode(chunk, { stream: true }), final);\n            if (final) {\n                if (this.t.decode().length)\n                    throw 'invalid utf-8 data';\n                this.t = null;\n            }\n            return;\n        }\n        if (!this.p)\n            throw 'stream finished';\n        var dat = new u8(this.p.length + chunk.length);\n        dat.set(this.p);\n        dat.set(chunk, this.p.length);\n        var _a = dutf8(dat), ch = _a[0], np = _a[1];\n        if (final) {\n            if (np.length)\n                throw 'invalid utf-8 data';\n            this.p = null;\n        }\n        else\n            this.p = np;\n        this.ondata(ch, final);\n    };\n    return DecodeUTF8;\n}());\nexport { DecodeUTF8 };\n/**\n * Streaming UTF-8 encoding\n */\nvar EncodeUTF8 = /*#__PURE__*/ (function () {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is encoded\n     */\n    function EncodeUTF8(cb) {\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be encoded to UTF-8\n     * @param chunk The string data to push\n     * @param final Whether this is the last chunk\n     */\n    EncodeUTF8.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback';\n        if (this.d)\n            throw 'stream finished';\n        this.ondata(strToU8(chunk), this.d = final || false);\n    };\n    return EncodeUTF8;\n}());\nexport { EncodeUTF8 };\n/**\n * Converts a string into a Uint8Array for use with compression/decompression methods\n * @param str The string to encode\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless decoding a binary string.\n * @returns The string encoded in UTF-8/Latin-1 binary\n */\nexport function strToU8(str, latin1) {\n    if (latin1) {\n        var ar_1 = new u8(str.length);\n        for (var i = 0; i < str.length; ++i)\n            ar_1[i] = str.charCodeAt(i);\n        return ar_1;\n    }\n    if (te)\n        return te.encode(str);\n    var l = str.length;\n    var ar = new u8(str.length + (str.length >> 1));\n    var ai = 0;\n    var w = function (v) { ar[ai++] = v; };\n    for (var i = 0; i < l; ++i) {\n        if (ai + 5 > ar.length) {\n            var n = new u8(ai + 8 + ((l - i) << 1));\n            n.set(ar);\n            ar = n;\n        }\n        var c = str.charCodeAt(i);\n        if (c < 128 || latin1)\n            w(c);\n        else if (c < 2048)\n            w(192 | (c >> 6)), w(128 | (c & 63));\n        else if (c > 55295 && c < 57344)\n            c = 65536 + (c & 1023 << 10) | (str.charCodeAt(++i) & 1023),\n                w(240 | (c >> 18)), w(128 | ((c >> 12) & 63)), w(128 | ((c >> 6) & 63)), w(128 | (c & 63));\n        else\n            w(224 | (c >> 12)), w(128 | ((c >> 6) & 63)), w(128 | (c & 63));\n    }\n    return slc(ar, 0, ai);\n}\n/**\n * Converts a Uint8Array to a string\n * @param dat The data to decode to string\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless encoding to binary string.\n * @returns The original UTF-8/Latin-1 string\n */\nexport function strFromU8(dat, latin1) {\n    if (latin1) {\n        var r = '';\n        for (var i = 0; i < dat.length; i += 16384)\n            r += String.fromCharCode.apply(null, dat.subarray(i, i + 16384));\n        return r;\n    }\n    else if (td)\n        return td.decode(dat);\n    else {\n        var _a = dutf8(dat), out = _a[0], ext = _a[1];\n        if (ext.length)\n            throw 'invalid utf-8 data';\n        return out;\n    }\n}\n;\n// deflate bit flag\nvar dbf = function (l) { return l == 1 ? 3 : l < 6 ? 2 : l == 9 ? 1 : 0; };\n// skip local zip header\nvar slzh = function (d, b) { return b + 30 + b2(d, b + 26) + b2(d, b + 28); };\n// read zip header\nvar zh = function (d, b, z) {\n    var fnl = b2(d, b + 28), fn = strFromU8(d.subarray(b + 46, b + 46 + fnl), !(b2(d, b + 8) & 2048)), es = b + 46 + fnl, bs = b4(d, b + 20);\n    var _a = z && bs == 4294967295 ? z64e(d, es) : [bs, b4(d, b + 24), b4(d, b + 42)], sc = _a[0], su = _a[1], off = _a[2];\n    return [b2(d, b + 10), sc, su, fn, es + b2(d, b + 30) + b2(d, b + 32), off];\n};\n// read zip64 extra field\nvar z64e = function (d, b) {\n    for (; b2(d, b) != 1; b += 4 + b2(d, b + 2))\n        ;\n    return [b8(d, b + 12), b8(d, b + 4), b8(d, b + 20)];\n};\n// extra field length\nvar exfl = function (ex) {\n    var le = 0;\n    if (ex) {\n        for (var k in ex) {\n            var l = ex[k].length;\n            if (l > 65535)\n                throw 'extra field too long';\n            le += l + 4;\n        }\n    }\n    return le;\n};\n// write zip header\nvar wzh = function (d, b, f, fn, u, c, ce, co) {\n    var fl = fn.length, ex = f.extra, col = co && co.length;\n    var exl = exfl(ex);\n    wbytes(d, b, ce != null ? 0x2014B50 : 0x4034B50), b += 4;\n    if (ce != null)\n        d[b++] = 20, d[b++] = f.os;\n    d[b] = 20, b += 2; // spec compliance? what's that?\n    d[b++] = (f.flag << 1) | (c == null && 8), d[b++] = u && 8;\n    d[b++] = f.compression & 255, d[b++] = f.compression >> 8;\n    var dt = new Date(f.mtime == null ? Date.now() : f.mtime), y = dt.getFullYear() - 1980;\n    if (y < 0 || y > 119)\n        throw 'date not in range 1980-2099';\n    wbytes(d, b, (y << 25) | ((dt.getMonth() + 1) << 21) | (dt.getDate() << 16) | (dt.getHours() << 11) | (dt.getMinutes() << 5) | (dt.getSeconds() >>> 1)), b += 4;\n    if (c != null) {\n        wbytes(d, b, f.crc);\n        wbytes(d, b + 4, c);\n        wbytes(d, b + 8, f.size);\n    }\n    wbytes(d, b + 12, fl);\n    wbytes(d, b + 14, exl), b += 16;\n    if (ce != null) {\n        wbytes(d, b, col);\n        wbytes(d, b + 6, f.attrs);\n        wbytes(d, b + 10, ce), b += 14;\n    }\n    d.set(fn, b);\n    b += fl;\n    if (exl) {\n        for (var k in ex) {\n            var exf = ex[k], l = exf.length;\n            wbytes(d, b, +k);\n            wbytes(d, b + 2, l);\n            d.set(exf, b + 4), b += 4 + l;\n        }\n    }\n    if (col)\n        d.set(co, b), b += col;\n    return b;\n};\n// write zip footer (end of central directory)\nvar wzf = function (o, b, c, d, e) {\n    wbytes(o, b, 0x6054B50); // skip disk\n    wbytes(o, b + 8, c);\n    wbytes(o, b + 10, c);\n    wbytes(o, b + 12, d);\n    wbytes(o, b + 16, e);\n};\n/**\n * A pass-through stream to keep data uncompressed in a ZIP archive.\n */\nvar ZipPassThrough = /*#__PURE__*/ (function () {\n    /**\n     * Creates a pass-through stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     */\n    function ZipPassThrough(filename) {\n        this.filename = filename;\n        this.c = crc();\n        this.size = 0;\n        this.compression = 0;\n    }\n    /**\n     * Processes a chunk and pushes to the output stream. You can override this\n     * method in a subclass for custom behavior, but by default this passes\n     * the data through. You must call this.ondata(err, chunk, final) at some\n     * point in this method.\n     * @param chunk The chunk to process\n     * @param final Whether this is the last chunk\n     */\n    ZipPassThrough.prototype.process = function (chunk, final) {\n        this.ondata(null, chunk, final);\n    };\n    /**\n     * Pushes a chunk to be added. If you are subclassing this with a custom\n     * compression algorithm, note that you must push data from the source\n     * file only, pre-compression.\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    ZipPassThrough.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback - add to ZIP archive before pushing';\n        this.c.p(chunk);\n        this.size += chunk.length;\n        if (final)\n            this.crc = this.c.d();\n        this.process(chunk, final || false);\n    };\n    return ZipPassThrough;\n}());\nexport { ZipPassThrough };\n// I don't extend because TypeScript extension adds 1kB of runtime bloat\n/**\n * Streaming DEFLATE compression for ZIP archives. Prefer using AsyncZipDeflate\n * for better performance\n */\nvar ZipDeflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */\n    function ZipDeflate(filename, opts) {\n        var _this_1 = this;\n        if (!opts)\n            opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new Deflate(opts, function (dat, final) {\n            _this_1.ondata(null, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n    }\n    ZipDeflate.prototype.process = function (chunk, final) {\n        try {\n            this.d.push(chunk, final);\n        }\n        catch (e) {\n            this.ondata(e, null, final);\n        }\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    ZipDeflate.prototype.push = function (chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return ZipDeflate;\n}());\nexport { ZipDeflate };\n/**\n * Asynchronous streaming DEFLATE compression for ZIP archives\n */\nvar AsyncZipDeflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */\n    function AsyncZipDeflate(filename, opts) {\n        var _this_1 = this;\n        if (!opts)\n            opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new AsyncDeflate(opts, function (err, dat, final) {\n            _this_1.ondata(err, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n        this.terminate = this.d.terminate;\n    }\n    AsyncZipDeflate.prototype.process = function (chunk, final) {\n        this.d.push(chunk, final);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncZipDeflate.prototype.push = function (chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return AsyncZipDeflate;\n}());\nexport { AsyncZipDeflate };\n// TODO: Better tree shaking\n/**\n * A zippable archive to which files can incrementally be added\n */\nvar Zip = /*#__PURE__*/ (function () {\n    /**\n     * Creates an empty ZIP archive to which files can be added\n     * @param cb The callback to call whenever data for the generated ZIP archive\n     *           is available\n     */\n    function Zip(cb) {\n        this.ondata = cb;\n        this.u = [];\n        this.d = 1;\n    }\n    /**\n     * Adds a file to the ZIP archive\n     * @param file The file stream to add\n     */\n    Zip.prototype.add = function (file) {\n        var _this_1 = this;\n        if (this.d & 2)\n            throw 'stream finished';\n        var f = strToU8(file.filename), fl = f.length;\n        var com = file.comment, o = com && strToU8(com);\n        var u = fl != file.filename.length || (o && (com.length != o.length));\n        var hl = fl + exfl(file.extra) + 30;\n        if (fl > 65535)\n            throw 'filename too long';\n        var header = new u8(hl);\n        wzh(header, 0, file, f, u);\n        var chks = [header];\n        var pAll = function () {\n            for (var _i = 0, chks_1 = chks; _i < chks_1.length; _i++) {\n                var chk = chks_1[_i];\n                _this_1.ondata(null, chk, false);\n            }\n            chks = [];\n        };\n        var tr = this.d;\n        this.d = 0;\n        var ind = this.u.length;\n        var uf = mrg(file, {\n            f: f,\n            u: u,\n            o: o,\n            t: function () {\n                if (file.terminate)\n                    file.terminate();\n            },\n            r: function () {\n                pAll();\n                if (tr) {\n                    var nxt = _this_1.u[ind + 1];\n                    if (nxt)\n                        nxt.r();\n                    else\n                        _this_1.d = 1;\n                }\n                tr = 1;\n            }\n        });\n        var cl = 0;\n        file.ondata = function (err, dat, final) {\n            if (err) {\n                _this_1.ondata(err, dat, final);\n                _this_1.terminate();\n            }\n            else {\n                cl += dat.length;\n                chks.push(dat);\n                if (final) {\n                    var dd = new u8(16);\n                    wbytes(dd, 0, 0x8074B50);\n                    wbytes(dd, 4, file.crc);\n                    wbytes(dd, 8, cl);\n                    wbytes(dd, 12, file.size);\n                    chks.push(dd);\n                    uf.c = cl, uf.b = hl + cl + 16, uf.crc = file.crc, uf.size = file.size;\n                    if (tr)\n                        uf.r();\n                    tr = 1;\n                }\n                else if (tr)\n                    pAll();\n            }\n        };\n        this.u.push(uf);\n    };\n    /**\n     * Ends the process of adding files and prepares to emit the final chunks.\n     * This *must* be called after adding all desired files for the resulting\n     * ZIP file to work properly.\n     */\n    Zip.prototype.end = function () {\n        var _this_1 = this;\n        if (this.d & 2) {\n            if (this.d & 1)\n                throw 'stream finishing';\n            throw 'stream finished';\n        }\n        if (this.d)\n            this.e();\n        else\n            this.u.push({\n                r: function () {\n                    if (!(_this_1.d & 1))\n                        return;\n                    _this_1.u.splice(-1, 1);\n                    _this_1.e();\n                },\n                t: function () { }\n            });\n        this.d = 3;\n    };\n    Zip.prototype.e = function () {\n        var bt = 0, l = 0, tl = 0;\n        for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n            var f = _a[_i];\n            tl += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0);\n        }\n        var out = new u8(tl + 22);\n        for (var _b = 0, _c = this.u; _b < _c.length; _b++) {\n            var f = _c[_b];\n            wzh(out, bt, f, f.f, f.u, f.c, l, f.o);\n            bt += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0), l += f.b;\n        }\n        wzf(out, bt, this.u.length, tl, l);\n        this.ondata(null, out, true);\n        this.d = 2;\n    };\n    /**\n     * A method to terminate any internal workers used by the stream. Subsequent\n     * calls to add() will fail.\n     */\n    Zip.prototype.terminate = function () {\n        for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n            var f = _a[_i];\n            f.t();\n        }\n        this.d = 2;\n    };\n    return Zip;\n}());\nexport { Zip };\nexport function zip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var r = {};\n    fltn(data, '', r, opts);\n    var k = Object.keys(r);\n    var lft = k.length, o = 0, tot = 0;\n    var slft = lft, files = new Array(lft);\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var cbf = function () {\n        var out = new u8(tot + 22), oe = o, cdl = tot - o;\n        tot = 0;\n        for (var i = 0; i < slft; ++i) {\n            var f = files[i];\n            try {\n                var l = f.c.length;\n                wzh(out, tot, f, f.f, f.u, l);\n                var badd = 30 + f.f.length + exfl(f.extra);\n                var loc = tot + badd;\n                out.set(f.c, loc);\n                wzh(out, o, f, f.f, f.u, l, tot, f.m), o += 16 + badd + (f.m ? f.m.length : 0), tot = loc + l;\n            }\n            catch (e) {\n                return cb(e, null);\n            }\n        }\n        wzf(out, o, files.length, cdl, oe);\n        cb(null, out);\n    };\n    if (!lft)\n        cbf();\n    var _loop_1 = function (i) {\n        var fn = k[i];\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var c = crc(), size = file.length;\n        c.p(file);\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        var compression = p.level == 0 ? 0 : 8;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                var l = d.length;\n                files[i] = mrg(p, {\n                    size: size,\n                    crc: c.d(),\n                    c: d,\n                    f: f,\n                    m: m,\n                    u: s != fn.length || (m && (com.length != ms)),\n                    compression: compression\n                });\n                o += 30 + s + exl + l;\n                tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n                if (!--lft)\n                    cbf();\n            }\n        };\n        if (s > 65535)\n            cbl('filename too long', null);\n        if (!compression)\n            cbl(null, file);\n        else if (size < 160000) {\n            try {\n                cbl(null, deflateSync(file, p));\n            }\n            catch (e) {\n                cbl(e, null);\n            }\n        }\n        else\n            term.push(deflate(file, p, cbl));\n    };\n    // Cannot use lft because it can decrease\n    for (var i = 0; i < slft; ++i) {\n        _loop_1(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously creates a ZIP file. Prefer using `zip` for better performance\n * with more than one file.\n * @param data The directory structure for the ZIP archive\n * @param opts The main options, merged with per-file options\n * @returns The generated ZIP archive\n */\nexport function zipSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var r = {};\n    var files = [];\n    fltn(data, '', r, opts);\n    var o = 0;\n    var tot = 0;\n    for (var fn in r) {\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var compression = p.level == 0 ? 0 : 8;\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        if (s > 65535)\n            throw 'filename too long';\n        var d = compression ? deflateSync(file, p) : file, l = d.length;\n        var c = crc();\n        c.p(file);\n        files.push(mrg(p, {\n            size: file.length,\n            crc: c.d(),\n            c: d,\n            f: f,\n            m: m,\n            u: s != fn.length || (m && (com.length != ms)),\n            o: o,\n            compression: compression\n        }));\n        o += 30 + s + exl + l;\n        tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n    }\n    var out = new u8(tot + 22), oe = o, cdl = tot - o;\n    for (var i = 0; i < files.length; ++i) {\n        var f = files[i];\n        wzh(out, f.o, f, f.f, f.u, f.c.length);\n        var badd = 30 + f.f.length + exfl(f.extra);\n        out.set(f.c, f.o + badd);\n        wzh(out, o, f, f.f, f.u, f.c.length, f.o, f.m), o += 16 + badd + (f.m ? f.m.length : 0);\n    }\n    wzf(out, o, files.length, cdl, oe);\n    return out;\n}\n/**\n * Streaming pass-through decompression for ZIP archives\n */\nvar UnzipPassThrough = /*#__PURE__*/ (function () {\n    function UnzipPassThrough() {\n    }\n    UnzipPassThrough.prototype.push = function (data, final) {\n        this.ondata(null, data, final);\n    };\n    UnzipPassThrough.compression = 0;\n    return UnzipPassThrough;\n}());\nexport { UnzipPassThrough };\n/**\n * Streaming DEFLATE decompression for ZIP archives. Prefer AsyncZipInflate for\n * better performance.\n */\nvar UnzipInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */\n    function UnzipInflate() {\n        var _this_1 = this;\n        this.i = new Inflate(function (dat, final) {\n            _this_1.ondata(null, dat, final);\n        });\n    }\n    UnzipInflate.prototype.push = function (data, final) {\n        try {\n            this.i.push(data, final);\n        }\n        catch (e) {\n            this.ondata(e, data, final);\n        }\n    };\n    UnzipInflate.compression = 8;\n    return UnzipInflate;\n}());\nexport { UnzipInflate };\n/**\n * Asynchronous streaming DEFLATE decompression for ZIP archives\n */\nvar AsyncUnzipInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */\n    function AsyncUnzipInflate(_, sz) {\n        var _this_1 = this;\n        if (sz < 320000) {\n            this.i = new Inflate(function (dat, final) {\n                _this_1.ondata(null, dat, final);\n            });\n        }\n        else {\n            this.i = new AsyncInflate(function (err, dat, final) {\n                _this_1.ondata(err, dat, final);\n            });\n            this.terminate = this.i.terminate;\n        }\n    }\n    AsyncUnzipInflate.prototype.push = function (data, final) {\n        if (this.i.terminate)\n            data = slc(data, 0);\n        this.i.push(data, final);\n    };\n    AsyncUnzipInflate.compression = 8;\n    return AsyncUnzipInflate;\n}());\nexport { AsyncUnzipInflate };\n/**\n * A ZIP archive decompression stream that emits files as they are discovered\n */\nvar Unzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates a ZIP decompression stream\n     * @param cb The callback to call whenever a file in the ZIP archive is found\n     */\n    function Unzip(cb) {\n        this.onfile = cb;\n        this.k = [];\n        this.o = {\n            0: UnzipPassThrough\n        };\n        this.p = et;\n    }\n    /**\n     * Pushes a chunk to be unzipped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzip.prototype.push = function (chunk, final) {\n        var _this_1 = this;\n        if (!this.onfile)\n            throw 'no callback';\n        if (!this.p)\n            throw 'stream finished';\n        if (this.c > 0) {\n            var len = Math.min(this.c, chunk.length);\n            var toAdd = chunk.subarray(0, len);\n            this.c -= len;\n            if (this.d)\n                this.d.push(toAdd, !this.c);\n            else\n                this.k[0].push(toAdd);\n            chunk = chunk.subarray(len);\n            if (chunk.length)\n                return this.push(chunk, final);\n        }\n        else {\n            var f = 0, i = 0, is = void 0, buf = void 0;\n            if (!this.p.length)\n                buf = chunk;\n            else if (!chunk.length)\n                buf = this.p;\n            else {\n                buf = new u8(this.p.length + chunk.length);\n                buf.set(this.p), buf.set(chunk, this.p.length);\n            }\n            var l = buf.length, oc = this.c, add = oc && this.d;\n            var _loop_2 = function () {\n                var _a;\n                var sig = b4(buf, i);\n                if (sig == 0x4034B50) {\n                    f = 1, is = i;\n                    this_1.d = null;\n                    this_1.c = 0;\n                    var bf = b2(buf, i + 6), cmp_1 = b2(buf, i + 8), u = bf & 2048, dd = bf & 8, fnl = b2(buf, i + 26), es = b2(buf, i + 28);\n                    if (l > i + 30 + fnl + es) {\n                        var chks_2 = [];\n                        this_1.k.unshift(chks_2);\n                        f = 2;\n                        var sc_1 = b4(buf, i + 18), su_1 = b4(buf, i + 22);\n                        var fn_1 = strFromU8(buf.subarray(i + 30, i += 30 + fnl), !u);\n                        if (sc_1 == 4294967295) {\n                            _a = dd ? [-2] : z64e(buf, i), sc_1 = _a[0], su_1 = _a[1];\n                        }\n                        else if (dd)\n                            sc_1 = -1;\n                        i += es;\n                        this_1.c = sc_1;\n                        var d_1;\n                        var file_1 = {\n                            name: fn_1,\n                            compression: cmp_1,\n                            start: function () {\n                                if (!file_1.ondata)\n                                    throw 'no callback';\n                                if (!sc_1)\n                                    file_1.ondata(null, et, true);\n                                else {\n                                    var ctr = _this_1.o[cmp_1];\n                                    if (!ctr)\n                                        throw 'unknown compression type ' + cmp_1;\n                                    d_1 = sc_1 < 0 ? new ctr(fn_1) : new ctr(fn_1, sc_1, su_1);\n                                    d_1.ondata = function (err, dat, final) { file_1.ondata(err, dat, final); };\n                                    for (var _i = 0, chks_3 = chks_2; _i < chks_3.length; _i++) {\n                                        var dat = chks_3[_i];\n                                        d_1.push(dat, false);\n                                    }\n                                    if (_this_1.k[0] == chks_2 && _this_1.c)\n                                        _this_1.d = d_1;\n                                    else\n                                        d_1.push(et, true);\n                                }\n                            },\n                            terminate: function () {\n                                if (d_1 && d_1.terminate)\n                                    d_1.terminate();\n                            }\n                        };\n                        if (sc_1 >= 0)\n                            file_1.size = sc_1, file_1.originalSize = su_1;\n                        this_1.onfile(file_1);\n                    }\n                    return \"break\";\n                }\n                else if (oc) {\n                    if (sig == 0x8074B50) {\n                        is = i += 12 + (oc == -2 && 8), f = 3, this_1.c = 0;\n                        return \"break\";\n                    }\n                    else if (sig == 0x2014B50) {\n                        is = i -= 4, f = 3, this_1.c = 0;\n                        return \"break\";\n                    }\n                }\n            };\n            var this_1 = this;\n            for (; i < l - 4; ++i) {\n                var state_1 = _loop_2();\n                if (state_1 === \"break\")\n                    break;\n            }\n            this.p = et;\n            if (oc < 0) {\n                var dat = f ? buf.subarray(0, is - 12 - (oc == -2 && 8) - (b4(buf, is - 16) == 0x8074B50 && 4)) : buf.subarray(0, i);\n                if (add)\n                    add.push(dat, !!f);\n                else\n                    this.k[+(f == 2)].push(dat);\n            }\n            if (f & 2)\n                return this.push(buf.subarray(i), final);\n            this.p = buf.subarray(i);\n        }\n        if (final) {\n            if (this.c)\n                throw 'invalid zip file';\n            this.p = null;\n        }\n    };\n    /**\n     * Registers a decoder with the stream, allowing for files compressed with\n     * the compression type provided to be expanded correctly\n     * @param decoder The decoder constructor\n     */\n    Unzip.prototype.register = function (decoder) {\n        this.o[decoder.compression] = decoder;\n    };\n    return Unzip;\n}());\nexport { Unzip };\n/**\n * Asynchronously decompresses a ZIP archive\n * @param data The raw compressed ZIP file\n * @param cb The callback to call with the decompressed files\n * @returns A function that can be used to immediately terminate the unzipping\n */\nexport function unzip(data, cb) {\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558) {\n            cb('invalid zip file', null);\n            return;\n        }\n    }\n    ;\n    var lft = b2(data, e + 8);\n    if (!lft)\n        cb(null, {});\n    var c = lft;\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50) {\n            cb('invalid zip file', null);\n            return;\n        }\n        c = lft = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    var _loop_3 = function (i) {\n        var _a = zh(data, o, z), c_1 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                files[fn] = d;\n                if (!--lft)\n                    cb(null, files);\n            }\n        };\n        if (!c_1)\n            cbl(null, slc(data, b, b + sc));\n        else if (c_1 == 8) {\n            var infl = data.subarray(b, b + sc);\n            if (sc < 320000) {\n                try {\n                    cbl(null, inflateSync(infl, new u8(su)));\n                }\n                catch (e) {\n                    cbl(e, null);\n                }\n            }\n            else\n                term.push(inflate(infl, { size: su }, cbl));\n        }\n        else\n            cbl('unknown compression type ' + c_1, null);\n    };\n    for (var i = 0; i < c; ++i) {\n        _loop_3(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously decompresses a ZIP archive. Prefer using `unzip` for better\n * performance with more than one file.\n * @param data The raw compressed ZIP file\n * @returns The decompressed files\n */\nexport function unzipSync(data) {\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558)\n            throw 'invalid zip file';\n    }\n    ;\n    var c = b2(data, e + 8);\n    if (!c)\n        return {};\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50)\n            throw 'invalid zip file';\n        c = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    for (var i = 0; i < c; ++i) {\n        var _a = zh(data, o, z), c_2 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        if (!c_2)\n            files[fn] = slc(data, b, b + sc);\n        else if (c_2 == 8)\n            files[fn] = inflateSync(data.subarray(b, b + sc), new u8(su));\n        else\n            throw 'unknown compression type ' + c_2;\n    }\n    return files;\n}\n"], "mappings": ";AASA,IAAI,MAAM,CAAC;AACX,IAAI,KAAM,SAAU,GAAG,IAAI,KAAK,UAAU,IAAI;AAC1C,MAAI,IAAI,IAAI,OAAO,IAAI,EAAE,MAAM,IAAI,EAAE,IAAI,IAAI,gBAAgB,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE,MAAM,kBAAkB,CAAC,CAAC,EAAE;AACzG,IAAE,UAAU,SAAU,GAAG;AAAE,WAAO,GAAG,EAAE,OAAO,IAAI;AAAA,EAAG;AACrD,IAAE,YAAY,SAAU,GAAG;AAAE,WAAO,GAAG,MAAM,EAAE,IAAI;AAAA,EAAG;AACtD,IAAE,YAAY,KAAK,QAAQ;AAC3B,SAAO;AACX;AAGA,IAAI,KAAK;AAAT,IAAqB,MAAM;AAA3B,IAAwC,MAAM;AAE9C,IAAI,OAAO,IAAI,GAAG;AAAA,EAAC;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA;AAAA,EAAgB;AAAA,EAAG;AAAA;AAAA,EAAoB;AAAC,CAAC;AAGhJ,IAAI,OAAO,IAAI,GAAG;AAAA,EAAC;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA;AAAA,EAAiB;AAAA,EAAG;AAAC,CAAC;AAEvI,IAAI,OAAO,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;AAEpF,IAAI,OAAO,SAAU,IAAI,OAAO;AAC5B,MAAI,IAAI,IAAI,IAAI,EAAE;AAClB,WAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,MAAE,CAAC,IAAI,SAAS,KAAK,GAAG,IAAI,CAAC;AAAA,EACjC;AAEA,MAAI,IAAI,IAAI,IAAI,EAAE,EAAE,CAAC;AACrB,WAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,aAAS,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG;AAClC,QAAE,CAAC,IAAM,IAAI,EAAE,CAAC,KAAM,IAAK;AAAA,IAC/B;AAAA,EACJ;AACA,SAAO,CAAC,GAAG,CAAC;AAChB;AACA,IAAI,KAAK,KAAK,MAAM,CAAC;AAArB,IAAwB,KAAK,GAAG,CAAC;AAAjC,IAAoC,QAAQ,GAAG,CAAC;AAEhD,GAAG,EAAE,IAAI,KAAK,MAAM,GAAG,IAAI;AAC3B,IAAI,KAAK,KAAK,MAAM,CAAC;AAArB,IAAwB,KAAK,GAAG,CAAC;AAAjC,IAAoC,QAAQ,GAAG,CAAC;AAEhD,IAAI,MAAM,IAAI,IAAI,KAAK;AACvB,KAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAExB,OAAM,IAAI,WAAY,KAAO,IAAI,UAAW;AAChD,OAAM,IAAI,WAAY,KAAO,IAAI,UAAW;AAC5C,OAAM,IAAI,WAAY,KAAO,IAAI,SAAW;AAC5C,MAAI,CAAC,MAAO,IAAI,WAAY,KAAO,IAAI,QAAW,OAAQ;AAC9D;AAJQ;AAFC;AAUT,IAAI,OAAQ,SAAU,IAAI,IAAI,GAAG;AAC7B,MAAI,IAAI,GAAG;AAEX,MAAI,IAAI;AAER,MAAI,IAAI,IAAI,IAAI,EAAE;AAElB,SAAO,IAAI,GAAG,EAAE;AACZ,MAAE,EAAE,GAAG,CAAC,IAAI,CAAC;AAEjB,MAAI,KAAK,IAAI,IAAI,EAAE;AACnB,OAAK,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACrB,OAAG,CAAC,IAAK,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAM;AAAA,EACtC;AACA,MAAI;AACJ,MAAI,GAAG;AAEH,SAAK,IAAI,IAAI,KAAK,EAAE;AAEpB,QAAI,MAAM,KAAK;AACf,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAEpB,UAAI,GAAG,CAAC,GAAG;AAEP,YAAI,KAAM,KAAK,IAAK,GAAG,CAAC;AAExB,YAAI,MAAM,KAAK,GAAG,CAAC;AAEnB,YAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO;AAE3B,iBAAS,IAAI,KAAM,KAAK,OAAO,GAAI,KAAK,GAAG,EAAE,GAAG;AAE5C,aAAG,IAAI,CAAC,MAAM,GAAG,IAAI;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,OACK;AACD,SAAK,IAAI,IAAI,CAAC;AACd,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACpB,UAAI,GAAG,CAAC,GAAG;AACP,WAAG,CAAC,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,MAAO,KAAK,GAAG,CAAC;AAAA,MAC/C;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAI,MAAM,IAAI,GAAG,GAAG;AACpB,KAAS,IAAI,GAAG,IAAI,KAAK,EAAE;AACvB,MAAI,CAAC,IAAI;AADJ;AAET,KAAS,IAAI,KAAK,IAAI,KAAK,EAAE;AACzB,MAAI,CAAC,IAAI;AADJ;AAET,KAAS,IAAI,KAAK,IAAI,KAAK,EAAE;AACzB,MAAI,CAAC,IAAI;AADJ;AAET,KAAS,IAAI,KAAK,IAAI,KAAK,EAAE;AACzB,MAAI,CAAC,IAAI;AADJ;AAGT,IAAI,MAAM,IAAI,GAAG,EAAE;AACnB,KAAS,IAAI,GAAG,IAAI,IAAI,EAAE;AACtB,MAAI,CAAC,IAAI;AADJ;AAGT,IAAI,MAAoB,KAAK,KAAK,GAAG,CAAC;AAAtC,IAAyC,OAAqB,KAAK,KAAK,GAAG,CAAC;AAE5E,IAAI,MAAoB,KAAK,KAAK,GAAG,CAAC;AAAtC,IAAyC,OAAqB,KAAK,KAAK,GAAG,CAAC;AAE5E,IAAI,MAAM,SAAU,GAAG;AACnB,MAAI,IAAI,EAAE,CAAC;AACX,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AAC/B,QAAI,EAAE,CAAC,IAAI;AACP,UAAI,EAAE,CAAC;AAAA,EACf;AACA,SAAO;AACX;AAEA,IAAI,OAAO,SAAU,GAAG,GAAG,GAAG;AAC1B,MAAI,IAAK,IAAI,IAAK;AAClB,UAAS,EAAE,CAAC,IAAK,EAAE,IAAI,CAAC,KAAK,OAAQ,IAAI,KAAM;AACnD;AAEA,IAAI,SAAS,SAAU,GAAG,GAAG;AACzB,MAAI,IAAK,IAAI,IAAK;AAClB,UAAS,EAAE,CAAC,IAAK,EAAE,IAAI,CAAC,KAAK,IAAM,EAAE,IAAI,CAAC,KAAK,QAAS,IAAI;AAChE;AAEA,IAAI,OAAO,SAAU,GAAG;AAAE,UAAS,IAAI,IAAK,MAAM,IAAI,KAAK;AAAI;AAG/D,IAAI,MAAM,SAAU,GAAG,GAAG,GAAG;AACzB,MAAI,KAAK,QAAQ,IAAI;AACjB,QAAI;AACR,MAAI,KAAK,QAAQ,IAAI,EAAE;AACnB,QAAI,EAAE;AAEV,MAAI,IAAI,KAAK,aAAa,MAAM,MAAM,aAAa,MAAM,MAAM,IAAI,IAAI,CAAC;AACxE,IAAE,IAAI,EAAE,SAAS,GAAG,CAAC,CAAC;AACtB,SAAO;AACX;AAEA,IAAI,QAAQ,SAAU,KAAK,KAAK,IAAI;AAEhC,MAAI,KAAK,IAAI;AACb,MAAI,CAAC,MAAO,MAAM,CAAC,GAAG,KAAK,KAAK;AAC5B,WAAO,OAAO,IAAI,GAAG,CAAC;AAE1B,MAAI,QAAQ,CAAC,OAAO;AAEpB,MAAI,OAAO,CAAC,MAAM,GAAG;AACrB,MAAI,CAAC;AACD,SAAK,CAAC;AAEV,MAAI,CAAC;AACD,UAAM,IAAI,GAAG,KAAK,CAAC;AAEvB,MAAI,OAAO,SAAUA,IAAG;AACpB,QAAI,KAAK,IAAI;AAEb,QAAIA,KAAI,IAAI;AAER,UAAI,OAAO,IAAI,GAAG,KAAK,IAAI,KAAK,GAAGA,EAAC,CAAC;AACrC,WAAK,IAAI,GAAG;AACZ,YAAM;AAAA,IACV;AAAA,EACJ;AAEA,MAAI,QAAQ,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG;AAEnG,MAAI,OAAO,KAAK;AAChB,KAAG;AACC,QAAI,CAAC,IAAI;AAEL,SAAG,IAAI,QAAQ,KAAK,KAAK,KAAK,CAAC;AAE/B,UAAI,OAAO,KAAK,KAAK,MAAM,GAAG,CAAC;AAC/B,aAAO;AACP,UAAI,CAAC,MAAM;AAEP,YAAI,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,IAAK,IAAI,IAAI,CAAC,KAAK,GAAI,IAAI,IAAI;AACnE,YAAI,IAAI,IAAI;AACR,cAAI;AACA,kBAAM;AACV;AAAA,QACJ;AAEA,YAAI;AACA,eAAK,KAAK,CAAC;AAEf,YAAI,IAAI,IAAI,SAAS,GAAG,CAAC,GAAG,EAAE;AAE9B,WAAG,IAAI,MAAM,GAAG,GAAG,IAAI,MAAM,IAAI;AACjC;AAAA,MACJ,WACS,QAAQ;AACb,aAAK,MAAM,KAAK,MAAM,MAAM,GAAG,MAAM;AAAA,eAChC,QAAQ,GAAG;AAEhB,YAAI,OAAO,KAAK,KAAK,KAAK,EAAE,IAAI,KAAK,QAAQ,KAAK,KAAK,MAAM,IAAI,EAAE,IAAI;AACvE,YAAI,KAAK,OAAO,KAAK,KAAK,MAAM,GAAG,EAAE,IAAI;AACzC,eAAO;AAEP,YAAI,MAAM,IAAI,GAAG,EAAE;AAEnB,YAAI,MAAM,IAAI,GAAG,EAAE;AACnB,iBAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAE5B,cAAI,KAAK,CAAC,CAAC,IAAI,KAAK,KAAK,MAAM,IAAI,GAAG,CAAC;AAAA,QAC3C;AACA,eAAO,QAAQ;AAEf,YAAI,MAAM,IAAI,GAAG,GAAG,UAAU,KAAK,OAAO;AAE1C,YAAI,MAAM,KAAK,KAAK,KAAK,CAAC;AAC1B,iBAAS,IAAI,GAAG,IAAI,MAAK;AACrB,cAAI,IAAI,IAAI,KAAK,KAAK,KAAK,MAAM,CAAC;AAElC,iBAAO,IAAI;AAEX,cAAI,IAAI,MAAM;AAEd,cAAI,IAAI,IAAI;AACR,gBAAI,GAAG,IAAI;AAAA,UACf,OACK;AAED,gBAAI,IAAI,GAAG,IAAI;AACf,gBAAI,KAAK;AACL,kBAAI,IAAI,KAAK,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,IAAI,IAAI,IAAI,CAAC;AAAA,qBAC7C,KAAK;AACV,kBAAI,IAAI,KAAK,KAAK,KAAK,CAAC,GAAG,OAAO;AAAA,qBAC7B,KAAK;AACV,kBAAI,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,OAAO;AACzC,mBAAO;AACH,kBAAI,GAAG,IAAI;AAAA,UACnB;AAAA,QACJ;AAEA,YAAI,KAAK,IAAI,SAAS,GAAG,IAAI,GAAG,KAAK,IAAI,SAAS,IAAI;AAEtD,cAAM,IAAI,EAAE;AAEZ,cAAM,IAAI,EAAE;AACZ,aAAK,KAAK,IAAI,KAAK,CAAC;AACpB,aAAK,KAAK,IAAI,KAAK,CAAC;AAAA,MACxB;AAEI,cAAM;AACV,UAAI,MAAM,MAAM;AACZ,YAAI;AACA,gBAAM;AACV;AAAA,MACJ;AAAA,IACJ;AAGA,QAAI;AACA,WAAK,KAAK,MAAM;AACpB,QAAI,OAAO,KAAK,OAAO,GAAG,OAAO,KAAK,OAAO;AAC7C,QAAI,OAAO;AACX,aAAQ,OAAO,KAAK;AAEhB,UAAI,IAAI,GAAG,OAAO,KAAK,GAAG,IAAI,GAAG,GAAG,MAAM,MAAM;AAChD,aAAO,IAAI;AACX,UAAI,MAAM,MAAM;AACZ,YAAI;AACA,gBAAM;AACV;AAAA,MACJ;AACA,UAAI,CAAC;AACD,cAAM;AACV,UAAI,MAAM;AACN,YAAI,IAAI,IAAI;AAAA,eACP,OAAO,KAAK;AACjB,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ,OACK;AACD,YAAI,MAAM,MAAM;AAEhB,YAAI,MAAM,KAAK;AAEX,cAAI,IAAI,MAAM,KAAK,IAAI,KAAK,CAAC;AAC7B,gBAAM,KAAK,KAAK,MAAM,KAAK,KAAK,CAAC,IAAI,GAAG,CAAC;AACzC,iBAAO;AAAA,QACX;AAEA,YAAI,IAAI,GAAG,OAAO,KAAK,GAAG,IAAI,GAAG,GAAG,OAAO,MAAM;AACjD,YAAI,CAAC;AACD,gBAAM;AACV,eAAO,IAAI;AACX,YAAI,KAAK,GAAG,IAAI;AAChB,YAAI,OAAO,GAAG;AACV,cAAI,IAAI,KAAK,IAAI;AACjB,gBAAM,OAAO,KAAK,GAAG,KAAM,KAAK,KAAK,GAAI,OAAO;AAAA,QACpD;AACA,YAAI,MAAM,MAAM;AACZ,cAAI;AACA,kBAAM;AACV;AAAA,QACJ;AACA,YAAI;AACA,eAAK,KAAK,MAAM;AACpB,YAAI,MAAM,KAAK;AACf,eAAO,KAAK,KAAK,MAAM,GAAG;AACtB,cAAI,EAAE,IAAI,IAAI,KAAK,EAAE;AACrB,cAAI,KAAK,CAAC,IAAI,IAAI,KAAK,IAAI,EAAE;AAC7B,cAAI,KAAK,CAAC,IAAI,IAAI,KAAK,IAAI,EAAE;AAC7B,cAAI,KAAK,CAAC,IAAI,IAAI,KAAK,IAAI,EAAE;AAAA,QACjC;AACA,aAAK;AAAA,MACT;AAAA,IACJ;AACA,OAAG,IAAI,IAAI,GAAG,IAAI,MAAM,GAAG,IAAI;AAC/B,QAAI;AACA,cAAQ,GAAG,GAAG,IAAI,KAAK,GAAG,IAAI,IAAI,GAAG,IAAI;AAAA,EACjD,SAAS,CAAC;AACV,SAAO,MAAM,IAAI,SAAS,MAAM,IAAI,KAAK,GAAG,EAAE;AAClD;AAEA,IAAI,QAAQ,SAAU,GAAG,GAAG,GAAG;AAC3B,QAAM,IAAI;AACV,MAAI,IAAK,IAAI,IAAK;AAClB,IAAE,CAAC,KAAK;AACR,IAAE,IAAI,CAAC,KAAK,MAAM;AACtB;AAEA,IAAI,UAAU,SAAU,GAAG,GAAG,GAAG;AAC7B,QAAM,IAAI;AACV,MAAI,IAAK,IAAI,IAAK;AAClB,IAAE,CAAC,KAAK;AACR,IAAE,IAAI,CAAC,KAAK,MAAM;AAClB,IAAE,IAAI,CAAC,KAAK,MAAM;AACtB;AAEA,IAAI,QAAQ,SAAU,GAAG,IAAI;AAEzB,MAAI,IAAI,CAAC;AACT,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AAC/B,QAAI,EAAE,CAAC;AACH,QAAE,KAAK,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC;AAAA,EAChC;AACA,MAAI,IAAI,EAAE;AACV,MAAI,KAAK,EAAE,MAAM;AACjB,MAAI,CAAC;AACD,WAAO,CAAC,IAAI,CAAC;AACjB,MAAI,KAAK,GAAG;AACR,QAAI,IAAI,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC;AACzB,MAAE,EAAE,CAAC,EAAE,CAAC,IAAI;AACZ,WAAO,CAAC,GAAG,CAAC;AAAA,EAChB;AACA,IAAE,KAAK,SAAU,GAAG,GAAG;AAAE,WAAO,EAAE,IAAI,EAAE;AAAA,EAAG,CAAC;AAG5C,IAAE,KAAK,EAAE,GAAG,IAAI,GAAG,MAAM,CAAC;AAC1B,MAAI,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;AAC7C,IAAE,CAAC,IAAI,EAAE,GAAG,IAAI,GAAG,EAAE,IAAI,EAAE,GAAG,GAAM,EAAK;AAMzC,SAAO,MAAM,IAAI,GAAG;AAChB,QAAI,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,OAAO,IAAI;AACrC,QAAI,EAAE,MAAM,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,OAAO,IAAI;AACjD,MAAE,IAAI,IAAI,EAAE,GAAG,IAAI,GAAG,EAAE,IAAI,EAAE,GAAG,GAAM,EAAK;AAAA,EAChD;AACA,MAAI,SAAS,GAAG,CAAC,EAAE;AACnB,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxB,QAAI,GAAG,CAAC,EAAE,IAAI;AACV,eAAS,GAAG,CAAC,EAAE;AAAA,EACvB;AAEA,MAAI,KAAK,IAAI,IAAI,SAAS,CAAC;AAE3B,MAAI,MAAM,GAAG,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC;AAC7B,MAAI,MAAM,IAAI;AAIV,QAAI,IAAI,GAAG,KAAK;AAEhB,QAAI,MAAM,MAAM,IAAI,MAAM,KAAK;AAC/B,OAAG,KAAK,SAAU,GAAG,GAAG;AAAE,aAAO,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE;AAAA,IAAG,CAAC;AAClE,WAAO,IAAI,GAAG,EAAE,GAAG;AACf,UAAI,OAAO,GAAG,CAAC,EAAE;AACjB,UAAI,GAAG,IAAI,IAAI,IAAI;AACf,cAAM,OAAO,KAAM,MAAM,GAAG,IAAI;AAChC,WAAG,IAAI,IAAI;AAAA,MACf;AAEI;AAAA,IACR;AACA,YAAQ;AACR,WAAO,KAAK,GAAG;AACX,UAAI,OAAO,GAAG,CAAC,EAAE;AACjB,UAAI,GAAG,IAAI,IAAI;AACX,cAAM,KAAM,KAAK,GAAG,IAAI,MAAM;AAAA;AAE9B,UAAE;AAAA,IACV;AACA,WAAO,KAAK,KAAK,IAAI,EAAE,GAAG;AACtB,UAAI,OAAO,GAAG,CAAC,EAAE;AACjB,UAAI,GAAG,IAAI,KAAK,IAAI;AAChB,UAAE,GAAG,IAAI;AACT,UAAE;AAAA,MACN;AAAA,IACJ;AACA,UAAM;AAAA,EACV;AACA,SAAO,CAAC,IAAI,GAAG,EAAE,GAAG,GAAG;AAC3B;AAEA,IAAI,KAAK,SAAU,GAAG,GAAG,GAAG;AACxB,SAAO,EAAE,KAAK,KACR,KAAK,IAAI,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC,IAC5C,EAAE,EAAE,CAAC,IAAI;AACpB;AAEA,IAAI,KAAK,SAAU,GAAG;AAClB,MAAI,IAAI,EAAE;AAEV,SAAO,KAAK,CAAC,EAAE,EAAE,CAAC;AACd;AACJ,MAAI,KAAK,IAAI,IAAI,EAAE,CAAC;AAEpB,MAAI,MAAM,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM;AAC/B,MAAI,IAAI,SAAU,GAAG;AAAE,OAAG,KAAK,IAAI;AAAA,EAAG;AACtC,WAAS,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AACzB,QAAI,EAAE,CAAC,KAAK,OAAO,KAAK;AACpB,QAAE;AAAA,SACD;AACD,UAAI,CAAC,OAAO,MAAM,GAAG;AACjB,eAAO,MAAM,KAAK,OAAO;AACrB,YAAE,KAAK;AACX,YAAI,MAAM,GAAG;AACT,YAAE,MAAM,KAAO,MAAM,MAAO,IAAK,QAAU,MAAM,KAAM,IAAK,KAAK;AACjE,gBAAM;AAAA,QACV;AAAA,MACJ,WACS,MAAM,GAAG;AACd,UAAE,GAAG,GAAG,EAAE;AACV,eAAO,MAAM,GAAG,OAAO;AACnB,YAAE,IAAI;AACV,YAAI,MAAM;AACN,YAAI,MAAM,KAAM,IAAK,IAAI,GAAG,MAAM;AAAA,MAC1C;AACA,aAAO;AACH,UAAE,GAAG;AACT,YAAM;AACN,YAAM,EAAE,CAAC;AAAA,IACb;AAAA,EACJ;AACA,SAAO,CAAC,GAAG,SAAS,GAAG,GAAG,GAAG,CAAC;AAClC;AAEA,IAAI,OAAO,SAAU,IAAI,IAAI;AACzB,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,EAAE;AAC7B,SAAK,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,SAAO;AACX;AAGA,IAAI,QAAQ,SAAU,KAAK,KAAK,KAAK;AAEjC,MAAI,IAAI,IAAI;AACZ,MAAI,IAAI,KAAK,MAAM,CAAC;AACpB,MAAI,CAAC,IAAI,IAAI;AACb,MAAI,IAAI,CAAC,IAAI,MAAM;AACnB,MAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;AACtB,MAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI;AAC1B,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE;AACrB,QAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;AAC1B,UAAQ,IAAI,IAAI,KAAK;AACzB;AAEA,IAAI,OAAO,SAAU,KAAK,KAAK,OAAO,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG;AACnE,QAAM,KAAK,KAAK,KAAK;AACrB,IAAE,GAAG,GAAG;AACR,MAAIC,MAAK,MAAM,IAAI,EAAE,GAAG,MAAMA,IAAG,CAAC,GAAG,MAAMA,IAAG,CAAC;AAC/C,MAAIC,MAAK,MAAM,IAAI,EAAE,GAAG,MAAMA,IAAG,CAAC,GAAG,MAAMA,IAAG,CAAC;AAC/C,MAAI,KAAK,GAAG,GAAG,GAAG,OAAO,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC;AAC1C,MAAI,KAAK,GAAG,GAAG,GAAG,OAAO,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC;AAC1C,MAAI,SAAS,IAAI,IAAI,EAAE;AACvB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE;AAC/B,WAAO,KAAK,CAAC,IAAI,EAAE;AACvB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE;AAC/B,WAAO,KAAK,CAAC,IAAI,EAAE;AACvB,MAAI,KAAK,MAAM,QAAQ,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC;AACnD,MAAI,OAAO;AACX,SAAO,OAAO,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,GAAG,EAAE;AACvC;AACJ,MAAI,OAAQ,KAAK,KAAM;AACvB,MAAI,QAAQ,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI;AAC5C,MAAI,QAAQ,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,OAAO,KAAK,QAAQ,GAAG,KAAK,IAAI,OAAO,EAAE,IAAI,IAAI,OAAO,EAAE,IAAI,IAAI,OAAO,EAAE;AACrI,MAAI,QAAQ,SAAS,QAAQ;AACzB,WAAO,MAAM,KAAK,GAAG,IAAI,SAAS,IAAI,KAAK,EAAE,CAAC;AAClD,MAAI,IAAI,IAAI,IAAI;AAChB,QAAM,KAAK,GAAG,KAAK,QAAQ,MAAM,GAAG,KAAK;AACzC,MAAI,QAAQ,OAAO;AACf,SAAK,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK;AAC/D,QAAI,MAAM,KAAK,KAAK,MAAM,CAAC;AAC3B,UAAM,KAAK,GAAG,MAAM,GAAG;AACvB,UAAM,KAAK,IAAI,GAAG,MAAM,CAAC;AACzB,UAAM,KAAK,IAAI,IAAI,OAAO,CAAC;AAC3B,SAAK;AACL,aAAS,IAAI,GAAG,IAAI,MAAM,EAAE;AACxB,YAAM,KAAK,IAAI,IAAI,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC;AACtC,SAAK,IAAI;AACT,QAAI,OAAO,CAAC,MAAM,IAAI;AACtB,aAAS,KAAK,GAAG,KAAK,GAAG,EAAE,IAAI;AAC3B,UAAI,OAAO,KAAK,EAAE;AAClB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAClC,YAAI,MAAM,KAAK,CAAC,IAAI;AACpB,cAAM,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG;AACrC,YAAI,MAAM;AACN,gBAAM,KAAK,GAAI,KAAK,CAAC,MAAM,IAAK,GAAG,GAAG,KAAK,KAAK,CAAC,MAAM;AAAA,MAC/D;AAAA,IACJ;AAAA,EACJ,OACK;AACD,SAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,EACvC;AACA,WAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,QAAI,KAAK,CAAC,IAAI,KAAK;AACf,UAAI,MAAO,KAAK,CAAC,MAAM,KAAM;AAC7B,cAAQ,KAAK,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,MAAM,GAAG;AACjD,UAAI,MAAM;AACN,cAAM,KAAK,GAAI,KAAK,CAAC,MAAM,KAAM,EAAE,GAAG,KAAK,KAAK,GAAG;AACvD,UAAI,MAAM,KAAK,CAAC,IAAI;AACpB,cAAQ,KAAK,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,GAAG;AACrC,UAAI,MAAM;AACN,gBAAQ,KAAK,GAAI,KAAK,CAAC,MAAM,IAAK,IAAI,GAAG,KAAK,KAAK,GAAG;AAAA,IAC9D,OACK;AACD,cAAQ,KAAK,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC;AAAA,IACjD;AAAA,EACJ;AACA,UAAQ,KAAK,GAAG,GAAG,GAAG,CAAC;AACvB,SAAO,IAAI,GAAG,GAAG;AACrB;AAEA,IAAI,MAAoB,IAAI,IAAI,CAAC,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAS,OAAO,CAAC;AAE3G,IAAI,KAAmB,IAAI,GAAG,CAAC;AAE/B,IAAI,OAAO,SAAU,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK;AACjD,MAAI,IAAI,IAAI;AACZ,MAAI,IAAI,IAAI,GAAG,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,GAAI,KAAK,IAAI;AAE7D,MAAI,IAAI,EAAE,SAAS,KAAK,EAAE,SAAS,IAAI;AACvC,MAAI,MAAM;AACV,MAAI,CAAC,OAAO,IAAI,GAAG;AACf,aAAS,IAAI,GAAG,KAAK,GAAG,KAAK,OAAO;AAEhC,UAAI,IAAI,IAAI;AACZ,UAAI,IAAI,GAAG;AAEP,cAAM,MAAM,GAAG,KAAK,IAAI,SAAS,GAAG,CAAC,CAAC;AAAA,MAC1C,OACK;AAED,UAAE,CAAC,IAAI;AACP,cAAM,MAAM,GAAG,KAAK,IAAI,SAAS,GAAG,CAAC,CAAC;AAAA,MAC1C;AAAA,IACJ;AAAA,EACJ,OACK;AACD,QAAI,MAAM,IAAI,MAAM,CAAC;AACrB,QAAI,IAAI,QAAQ,IAAI,IAAI,MAAM;AAC9B,QAAI,SAAS,KAAK,QAAQ;AAE1B,QAAI,OAAO,IAAI,IAAI,KAAK,GAAG,OAAO,IAAI,IAAI,QAAQ,CAAC;AACnD,QAAI,QAAQ,KAAK,KAAK,OAAO,CAAC,GAAG,QAAQ,IAAI;AAC7C,QAAI,MAAM,SAAUC,IAAG;AAAE,cAAQ,IAAIA,EAAC,IAAK,IAAIA,KAAI,CAAC,KAAK,QAAU,IAAIA,KAAI,CAAC,KAAK,SAAU;AAAA,IAAO;AAGlG,QAAI,OAAO,IAAI,IAAI,IAAK;AAExB,QAAI,KAAK,IAAI,IAAI,GAAG,GAAG,KAAK,IAAI,IAAI,EAAE;AAEtC,QAAI,OAAO,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;AAClD,WAAO,IAAI,GAAG,EAAE,GAAG;AAGf,UAAI,KAAK,IAAI,CAAC;AAEd,UAAI,OAAO,IAAI,OAAO,QAAQ,KAAK,EAAE;AACrC,WAAK,IAAI,IAAI;AACb,WAAK,EAAE,IAAI;AAGX,UAAI,MAAM,GAAG;AAET,YAAI,MAAM,IAAI;AACd,aAAK,OAAO,OAAQ,KAAK,UAAU,MAAM,KAAK;AAC1C,gBAAM,KAAK,KAAK,GAAG,GAAG,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG;AAC3D,eAAK,OAAO,KAAK,GAAG,KAAK;AACzB,mBAAS,IAAI,GAAG,IAAI,KAAK,EAAE;AACvB,eAAG,CAAC,IAAI;AACZ,mBAAS,IAAI,GAAG,IAAI,IAAI,EAAE;AACtB,eAAG,CAAC,IAAI;AAAA,QAChB;AAEA,YAAI,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG,MAAO,OAAO,QAAS;AACnD,YAAI,MAAM,KAAK,MAAM,IAAI,IAAI,GAAG,GAAG;AAC/B,cAAI,OAAO,KAAK,IAAI,GAAG,GAAG,IAAI;AAC9B,cAAI,OAAO,KAAK,IAAI,OAAO,CAAC;AAG5B,cAAI,KAAK,KAAK,IAAI,KAAK,GAAG;AAC1B,iBAAO,OAAO,QAAQ,EAAE,QAAQ,QAAQ,OAAO;AAC3C,gBAAI,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,GAAG,GAAG;AAChC,kBAAI,KAAK;AACT,qBAAO,KAAK,MAAM,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,KAAK,GAAG,GAAG,EAAE;AAClD;AACJ,kBAAI,KAAK,GAAG;AACR,oBAAI,IAAI,IAAI;AAEZ,oBAAI,KAAK;AACL;AAIJ,oBAAI,MAAM,KAAK,IAAI,KAAK,KAAK,CAAC;AAC9B,oBAAI,KAAK;AACT,yBAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC1B,sBAAI,KAAM,IAAI,MAAM,IAAI,QAAS;AACjC,sBAAI,MAAM,KAAK,EAAE;AACjB,sBAAI,KAAM,KAAK,MAAM,QAAS;AAC9B,sBAAI,KAAK;AACL,yBAAK,IAAI,QAAQ;AAAA,gBACzB;AAAA,cACJ;AAAA,YACJ;AAEA,mBAAO,OAAO,QAAQ,KAAK,IAAI;AAC/B,mBAAQ,OAAO,QAAQ,QAAS;AAAA,UACpC;AAAA,QACJ;AAEA,YAAI,GAAG;AAGH,eAAK,IAAI,IAAI,YAAa,MAAM,CAAC,KAAK,KAAM,MAAM,CAAC;AACnD,cAAI,MAAM,MAAM,CAAC,IAAI,IAAI,MAAM,MAAM,CAAC,IAAI;AAC1C,gBAAM,KAAK,GAAG,IAAI,KAAK,GAAG;AAC1B,YAAE,GAAG,MAAM,GAAG;AACd,YAAE,GAAG,GAAG;AACR,eAAK,IAAI;AACT,YAAE;AAAA,QACN,OACK;AACD,eAAK,IAAI,IAAI,IAAI,CAAC;AAClB,YAAE,GAAG,IAAI,CAAC,CAAC;AAAA,QACf;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,KAAK,KAAK,GAAG,KAAK,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG;AAE7D,QAAI,CAAC,OAAO,MAAM;AACd,YAAM,MAAM,GAAG,MAAM,GAAG,EAAE;AAAA,EAClC;AACA,SAAO,IAAI,GAAG,GAAG,MAAM,KAAK,GAAG,IAAI,IAAI;AAC3C;AAEA,IAAI,OAAsB,WAAY;AAClC,MAAI,IAAI,IAAI,WAAW,GAAG;AAC1B,WAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC1B,QAAI,IAAI,GAAG,IAAI;AACf,WAAO,EAAE;AACL,WAAM,IAAI,KAAM,cAAe,MAAM;AACzC,MAAE,CAAC,IAAI;AAAA,EACX;AACA,SAAO;AACX,EAAG;AAEH,IAAI,MAAM,WAAY;AAClB,MAAI,IAAI;AACR,SAAO;AAAA,IACH,GAAG,SAAU,GAAG;AAEZ,UAAI,KAAK;AACT,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE;AAC5B,aAAK,KAAM,KAAK,MAAO,EAAE,CAAC,CAAC,IAAK,OAAO;AAC3C,UAAI;AAAA,IACR;AAAA,IACA,GAAG,WAAY;AAAE,aAAO,CAAC;AAAA,IAAG;AAAA,EAChC;AACJ;AAEA,IAAI,QAAQ,WAAY;AACpB,MAAI,IAAI,GAAG,IAAI;AACf,SAAO;AAAA,IACH,GAAG,SAAU,GAAG;AAEZ,UAAI,IAAI,GAAG,IAAI;AACf,UAAI,IAAI,EAAE;AACV,eAAS,IAAI,GAAG,KAAK,KAAI;AACrB,YAAI,IAAI,KAAK,IAAI,IAAI,MAAM,CAAC;AAC5B,eAAO,IAAI,GAAG,EAAE;AACZ,eAAK,KAAK,EAAE,CAAC;AACjB,aAAK,IAAI,SAAS,MAAM,KAAK,KAAK,KAAK,IAAI,SAAS,MAAM,KAAK;AAAA,MACnE;AACA,UAAI,GAAG,IAAI;AAAA,IACf;AAAA,IACA,GAAG,WAAY;AACX,WAAK,OAAO,KAAK;AACjB,cAAQ,IAAI,QAAQ,KAAM,MAAM,KAAM,MAAM,IAAI,QAAQ,IAAK,MAAM;AAAA,IACvE;AAAA,EACJ;AACJ;AAGA,IAAI,OAAO,SAAU,KAAK,KAAK,KAAK,MAAM,IAAI;AAC1C,SAAO,KAAK,KAAK,IAAI,SAAS,OAAO,IAAI,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,KAAK,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,MAAM,CAAC,CAAC,IAAI,GAAG,IAAK,KAAK,IAAI,KAAM,KAAK,MAAM,CAAC,EAAE;AAC3K;AAEA,IAAI,MAAM,SAAU,GAAG,GAAG;AACtB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK;AACV,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,WAAS,KAAK;AACV,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,SAAO;AACX;AAQA,IAAI,OAAO,SAAU,IAAI,OAAOC,KAAI;AAChC,MAAI,KAAK,GAAG;AACZ,MAAI,KAAK,GAAG,SAAS;AACrB,MAAI,KAAK,GAAG,MAAM,GAAG,QAAQ,GAAG,IAAI,GAAG,GAAG,YAAY,GAAG,CAAC,EAAE,QAAQ,MAAM,EAAE,EAAE,MAAM,GAAG;AACvF,WAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,EAAE,GAAG;AAChC,QAAI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC;AACvB,QAAI,OAAO,KAAK,YAAY;AACxB,eAAS,MAAM,IAAI;AACnB,UAAI,OAAO,EAAE,SAAS;AACtB,UAAI,EAAE,WAAW;AAEb,YAAI,KAAK,QAAQ,eAAe,KAAK,IAAI;AACrC,cAAI,QAAQ,KAAK,QAAQ,KAAK,CAAC,IAAI;AACnC,mBAAS,KAAK,MAAM,OAAO,KAAK,QAAQ,KAAK,KAAK,CAAC;AAAA,QACvD,OACK;AACD,mBAAS;AACT,mBAAS,KAAK,EAAE;AACZ,qBAAS,MAAM,IAAI,gBAAgB,IAAI,MAAM,EAAE,UAAU,CAAC,EAAE,SAAS;AAAA,QAC7E;AAAA,MACJ;AAEI,iBAAS;AAAA,IACjB;AAEI,MAAAA,IAAG,CAAC,IAAI;AAAA,EAChB;AACA,SAAO,CAAC,OAAOA,GAAE;AACrB;AACA,IAAI,KAAK,CAAC;AAEV,IAAI,OAAO,SAAU,GAAG;AACpB,MAAI,KAAK,CAAC;AACV,WAAS,KAAK,GAAG;AACb,QAAI,EAAE,CAAC,aAAa,MAAM,EAAE,CAAC,aAAa,OAAO,EAAE,CAAC,aAAa;AAC7D,SAAG,MAAM,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,GAAG,MAAM;AAAA,EAC1D;AACA,SAAO;AACX;AAEA,IAAI,OAAO,SAAU,KAAK,MAAM,IAAI,IAAI;AACpC,MAAIC;AACJ,MAAI,CAAC,GAAG,EAAE,GAAG;AACT,QAAI,QAAQ,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,SAAS;AAC5C,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE;AACrB,MAAAA,MAAK,KAAK,IAAI,CAAC,GAAG,OAAO,IAAI,GAAG,QAAQA,IAAG,CAAC,GAAG,OAAOA,IAAG,CAAC;AAC9D,OAAG,EAAE,IAAI,KAAK,IAAI,CAAC,GAAG,OAAO,IAAI;AAAA,EACrC;AACA,MAAID,MAAK,IAAI,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;AAC1B,SAAO,GAAG,GAAG,EAAE,EAAE,CAAC,IAAI,4EAA4E,KAAK,SAAS,IAAI,KAAK,IAAIA,KAAI,KAAKA,GAAE,GAAG,EAAE;AACjJ;AAEA,IAAI,SAAS,WAAY;AAAE,SAAO,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,MAAM,IAAI,IAAI,MAAM,MAAM,KAAK,MAAM,KAAK,MAAM,QAAQ,MAAM,KAAK,OAAO,aAAa,KAAK,GAAG;AAAG;AAC/J,IAAI,QAAQ,WAAY;AAAE,SAAO,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,MAAM,OAAO,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,MAAM,OAAO,SAAS,OAAO,IAAI,IAAI,MAAM,OAAO,MAAM,MAAM,KAAK,MAAM,MAAM,aAAa,GAAG;AAAG;AAIpN,IAAI,OAAO,WAAY;AAAE,SAAO,CAAC,KAAK,GAAG;AAAG;AAI5C,IAAI,OAAO,WAAY;AAAE,SAAO,CAAC,GAAG;AAAG;AAEvC,IAAI,MAAM,SAAU,KAAK;AAAE,SAAO,YAAY,KAAK,CAAC,IAAI,MAAM,CAAC;AAAG;AAElE,IAAI,MAAM,SAAU,GAAG;AAAE,SAAO,KAAK,EAAE,QAAQ,IAAI,GAAG,EAAE,IAAI;AAAG;AAW/D,IAAI,QAAQ,SAAU,MAAM;AACxB,OAAK,SAAS,SAAU,KAAK,OAAO;AAAE,WAAO,YAAY,CAAC,KAAK,KAAK,GAAG,CAAC,IAAI,MAAM,CAAC;AAAA,EAAG;AACtF,SAAO,SAAU,IAAI;AAAE,WAAO,KAAK,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;AAAA,EAAG;AACrE;AAEA,IAAI,WAAW,SAAU,KAAK,MAAM,MAAM,MAAM,IAAI;AAChD,MAAI;AACJ,MAAI,IAAI,KAAK,KAAK,MAAM,IAAI,SAAU,KAAK,KAAK;AAC5C,QAAI;AACA,QAAE,UAAU,GAAG,KAAK,OAAO,KAAK,MAAM,GAAG;AAAA,SACxC;AACD,UAAI,IAAI,CAAC;AACL,UAAE,UAAU;AAChB,WAAK,OAAO,KAAK,MAAM,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,IAC9C;AAAA,EACJ,CAAC;AACD,IAAE,YAAY,IAAI;AAClB,OAAK,OAAO,SAAU,GAAG,GAAG;AACxB,QAAI;AACA,YAAM;AACV,QAAI,CAAC,KAAK;AACN,YAAM;AACV,MAAE,YAAY,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC;AAAA,EACxC;AACA,OAAK,YAAY,WAAY;AAAE,MAAE,UAAU;AAAA,EAAG;AAClD;AAEA,IAAI,KAAK,SAAU,GAAG,GAAG;AAAE,SAAO,EAAE,CAAC,IAAK,EAAE,IAAI,CAAC,KAAK;AAAI;AAE1D,IAAI,KAAK,SAAU,GAAG,GAAG;AAAE,UAAQ,EAAE,CAAC,IAAK,EAAE,IAAI,CAAC,KAAK,IAAM,EAAE,IAAI,CAAC,KAAK,KAAO,EAAE,IAAI,CAAC,KAAK,QAAS;AAAG;AACxG,IAAI,KAAK,SAAU,GAAG,GAAG;AAAE,SAAO,GAAG,GAAG,CAAC,IAAK,GAAG,GAAG,IAAI,CAAC,IAAI;AAAa;AAE1E,IAAI,SAAS,SAAU,GAAG,GAAG,GAAG;AAC5B,SAAO,GAAG,EAAE;AACR,MAAE,CAAC,IAAI,GAAG,OAAO;AACzB;AAEA,IAAI,MAAM,SAAU,GAAG,GAAG;AACtB,MAAI,KAAK,EAAE;AACX,IAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,QAAQ,IAAI,IAAI,EAAE,SAAS,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI;AACvF,MAAI,EAAE,SAAS;AACX,WAAO,GAAG,GAAG,KAAK,MAAM,IAAI,KAAK,EAAE,SAAS,KAAK,IAAI,CAAC,IAAI,GAAI,CAAC;AACnE,MAAI,IAAI;AACJ,MAAE,CAAC,IAAI;AACP,aAAS,IAAI,GAAG,KAAK,GAAG,QAAQ,EAAE;AAC9B,QAAE,IAAI,EAAE,IAAI,GAAG,WAAW,CAAC;AAAA,EACnC;AACJ;AAGA,IAAI,MAAM,SAAU,GAAG;AACnB,MAAI,EAAE,CAAC,KAAK,MAAM,EAAE,CAAC,KAAK,OAAO,EAAE,CAAC,KAAK;AACrC,UAAM;AACV,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,KAAK;AACT,MAAI,MAAM;AACN,UAAM,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,KAAK;AACjC,WAAS,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,IAAI,KAAK,GAAG,MAAM,CAAC,EAAE,IAAI;AAChE;AACJ,SAAO,MAAM,MAAM;AACvB;AAEA,IAAI,MAAM,SAAU,GAAG;AACnB,MAAI,IAAI,EAAE;AACV,UAAS,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE,IAAI,CAAC,KAAK,KAAO,EAAE,IAAI,CAAC,KAAK,QAAS;AAChF;AAEA,IAAI,OAAO,SAAU,GAAG;AAAE,SAAO,MAAO,EAAE,YAAa,EAAE,SAAS,SAAS,KAAO;AAAI;AAEtF,IAAI,MAAM,SAAU,GAAG,GAAG;AACtB,MAAI,KAAK,EAAE,OAAOE,MAAK,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI;AAChE,IAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAKA,OAAM,KAAMA,MAAM,KAAK,IAAIA,MAAM;AACzD;AAEA,IAAI,MAAM,SAAU,GAAG;AACnB,OAAK,EAAE,CAAC,IAAI,OAAO,KAAM,EAAE,CAAC,MAAM,IAAK,MAAO,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK;AAC9D,UAAM;AACV,MAAI,EAAE,CAAC,IAAI;AACP,UAAM;AACd;AACA,SAAS,aAAa,MAAM,IAAI;AAC5B,MAAI,CAAC,MAAM,OAAO,QAAQ;AACtB,SAAK,MAAM,OAAO,CAAC;AACvB,OAAK,SAAS;AACd,SAAO;AACX;AAKA,IAAI,UAAyB,WAAY;AACrC,WAASC,SAAQ,MAAM,IAAI;AACvB,QAAI,CAAC,MAAM,OAAO,QAAQ;AACtB,WAAK,MAAM,OAAO,CAAC;AACvB,SAAK,SAAS;AACd,SAAK,IAAI,QAAQ,CAAC;AAAA,EACtB;AACA,EAAAA,SAAQ,UAAU,IAAI,SAAU,GAAG,GAAG;AAClC,SAAK,OAAO,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;AAAA,EAC5C;AAMA,EAAAA,SAAQ,UAAU,OAAO,SAAU,OAAO,OAAO;AAC7C,QAAI,KAAK;AACL,YAAM;AACV,QAAI,CAAC,KAAK;AACN,YAAM;AACV,SAAK,IAAI;AACT,SAAK,EAAE,OAAO,SAAS,KAAK;AAAA,EAChC;AACA,SAAOA;AACX,EAAE;AAKF,IAAI,eAA8B,2BAAY;AAC1C,WAASC,cAAa,MAAM,IAAI;AAC5B,aAAS;AAAA,MACL;AAAA,MACA,WAAY;AAAE,eAAO,CAAC,OAAO,OAAO;AAAA,MAAG;AAAA,IAC3C,GAAG,MAAM,aAAa,KAAK,MAAM,MAAM,EAAE,GAAG,SAAU,IAAI;AACtD,UAAI,OAAO,IAAI,QAAQ,GAAG,IAAI;AAC9B,kBAAY,MAAM,IAAI;AAAA,IAC1B,GAAG,CAAC;AAAA,EACR;AACA,SAAOA;AACX,EAAE;AAiBK,SAAS,YAAY,MAAM,MAAM;AACpC,SAAO,KAAK,MAAM,QAAQ,CAAC,GAAG,GAAG,CAAC;AACtC;AAIA,IAAI,UAAyB,WAAY;AAKrC,WAASC,SAAQ,IAAI;AACjB,SAAK,IAAI,CAAC;AACV,SAAK,IAAI,IAAI,GAAG,CAAC;AACjB,SAAK,SAAS;AAAA,EAClB;AACA,EAAAA,SAAQ,UAAU,IAAI,SAAU,GAAG;AAC/B,QAAI,KAAK;AACL,YAAM;AACV,QAAI,CAAC,KAAK;AACN,YAAM;AACV,QAAI,IAAI,KAAK,EAAE;AACf,QAAI,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM;AAC3B,MAAE,IAAI,KAAK,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI;AAAA,EACzC;AACA,EAAAA,SAAQ,UAAU,IAAI,SAAU,OAAO;AACnC,SAAK,IAAI,KAAK,EAAE,IAAI,SAAS;AAC7B,QAAI,MAAM,KAAK,EAAE;AACjB,QAAI,KAAK,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACrC,SAAK,OAAO,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC;AAC1C,SAAK,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,KAAK,GAAG,KAAK,EAAE,IAAI,KAAK,EAAE;AACtD,SAAK,IAAI,IAAI,KAAK,GAAI,KAAK,EAAE,IAAI,IAAK,CAAC,GAAG,KAAK,EAAE,KAAK;AAAA,EAC1D;AAMA,EAAAA,SAAQ,UAAU,OAAO,SAAU,OAAO,OAAO;AAC7C,SAAK,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK;AAAA,EAC/B;AACA,SAAOA;AACX,EAAE;AAKF,IAAI,eAA8B,2BAAY;AAK1C,WAASC,cAAa,IAAI;AACtB,SAAK,SAAS;AACd,aAAS;AAAA,MACL;AAAA,MACA,WAAY;AAAE,eAAO,CAAC,OAAO,OAAO;AAAA,MAAG;AAAA,IAC3C,GAAG,MAAM,GAAG,WAAY;AACpB,UAAI,OAAO,IAAI,QAAQ;AACvB,kBAAY,MAAM,IAAI;AAAA,IAC1B,GAAG,CAAC;AAAA,EACR;AACA,SAAOA;AACX,EAAE;AAiBK,SAAS,YAAY,MAAM,KAAK;AACnC,SAAO,MAAM,MAAM,GAAG;AAC1B;AAKA,IAAI,OAAsB,WAAY;AAClC,WAASC,MAAK,MAAM,IAAI;AACpB,SAAK,IAAI,IAAI;AACb,SAAK,IAAI;AACT,SAAK,IAAI;AACT,YAAQ,KAAK,MAAM,MAAM,EAAE;AAAA,EAC/B;AAMA,EAAAA,MAAK,UAAU,OAAO,SAAU,OAAO,OAAO;AAC1C,YAAQ,UAAU,KAAK,KAAK,MAAM,OAAO,KAAK;AAAA,EAClD;AACA,EAAAA,MAAK,UAAU,IAAI,SAAU,GAAG,GAAG;AAC/B,SAAK,EAAE,EAAE,CAAC;AACV,SAAK,KAAK,EAAE;AACZ,QAAI,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;AAC5D,QAAI,KAAK;AACL,UAAI,KAAK,KAAK,CAAC,GAAG,KAAK,IAAI;AAC/B,QAAI;AACA,aAAO,KAAK,IAAI,SAAS,GAAG,KAAK,EAAE,EAAE,CAAC,GAAG,OAAO,KAAK,IAAI,SAAS,GAAG,KAAK,CAAC;AAC/E,SAAK,OAAO,KAAK,CAAC;AAAA,EACtB;AACA,SAAOA;AACX,EAAE;AA+CF,IAAI,SAAwB,WAAY;AAKpC,WAASC,QAAO,IAAI;AAChB,SAAK,IAAI;AACT,YAAQ,KAAK,MAAM,EAAE;AAAA,EACzB;AAMA,EAAAA,QAAO,UAAU,OAAO,SAAU,OAAO,OAAO;AAC5C,YAAQ,UAAU,EAAE,KAAK,MAAM,KAAK;AACpC,QAAI,KAAK,GAAG;AACR,UAAI,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK,CAAC,IAAI;AAC1C,UAAI,KAAK,KAAK,EAAE,UAAU,CAAC;AACvB;AACJ,WAAK,IAAI,KAAK,EAAE,SAAS,CAAC,GAAG,KAAK,IAAI;AAAA,IAC1C;AACA,QAAI,OAAO;AACP,UAAI,KAAK,EAAE,SAAS;AAChB,cAAM;AACV,WAAK,IAAI,KAAK,EAAE,SAAS,GAAG,EAAE;AAAA,IAClC;AAGA,YAAQ,UAAU,EAAE,KAAK,MAAM,KAAK;AAAA,EACxC;AACA,SAAOA;AACX,EAAE;AAKF,IAAI,cAA6B,2BAAY;AAKzC,WAASC,aAAY,IAAI;AACrB,SAAK,SAAS;AACd,aAAS;AAAA,MACL;AAAA,MACA;AAAA,MACA,WAAY;AAAE,eAAO,CAAC,OAAO,SAAS,MAAM;AAAA,MAAG;AAAA,IACnD,GAAG,MAAM,GAAG,WAAY;AACpB,UAAI,OAAO,IAAI,OAAO;AACtB,kBAAY,MAAM,IAAI;AAAA,IAC1B,GAAG,CAAC;AAAA,EACR;AACA,SAAOA;AACX,EAAE;AAyBF,IAAI,OAAsB,WAAY;AAClC,WAASC,MAAK,MAAM,IAAI;AACpB,SAAK,IAAI,MAAM;AACf,SAAK,IAAI;AACT,YAAQ,KAAK,MAAM,MAAM,EAAE;AAAA,EAC/B;AAMA,EAAAA,MAAK,UAAU,OAAO,SAAU,OAAO,OAAO;AAC1C,YAAQ,UAAU,KAAK,KAAK,MAAM,OAAO,KAAK;AAAA,EAClD;AACA,EAAAA,MAAK,UAAU,IAAI,SAAU,GAAG,GAAG;AAC/B,SAAK,EAAE,EAAE,CAAC;AACV,QAAI,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;AACjD,QAAI,KAAK;AACL,UAAI,KAAK,KAAK,CAAC,GAAG,KAAK,IAAI;AAC/B,QAAI;AACA,aAAO,KAAK,IAAI,SAAS,GAAG,KAAK,EAAE,EAAE,CAAC;AAC1C,SAAK,OAAO,KAAK,CAAC;AAAA,EACtB;AACA,SAAOA;AACX,EAAE;AA+CF,IAAI,SAAwB,WAAY;AAKpC,WAASC,QAAO,IAAI;AAChB,SAAK,IAAI;AACT,YAAQ,KAAK,MAAM,EAAE;AAAA,EACzB;AAMA,EAAAA,QAAO,UAAU,OAAO,SAAU,OAAO,OAAO;AAC5C,YAAQ,UAAU,EAAE,KAAK,MAAM,KAAK;AACpC,QAAI,KAAK,GAAG;AACR,UAAI,KAAK,EAAE,SAAS,KAAK,CAAC;AACtB;AACJ,WAAK,IAAI,KAAK,EAAE,SAAS,CAAC,GAAG,KAAK,IAAI;AAAA,IAC1C;AACA,QAAI,OAAO;AACP,UAAI,KAAK,EAAE,SAAS;AAChB,cAAM;AACV,WAAK,IAAI,KAAK,EAAE,SAAS,GAAG,EAAE;AAAA,IAClC;AAGA,YAAQ,UAAU,EAAE,KAAK,MAAM,KAAK;AAAA,EACxC;AACA,SAAOA;AACX,EAAE;AAKF,IAAI,cAA6B,2BAAY;AAKzC,WAASC,aAAY,IAAI;AACrB,SAAK,SAAS;AACd,aAAS;AAAA,MACL;AAAA,MACA;AAAA,MACA,WAAY;AAAE,eAAO,CAAC,OAAO,SAAS,MAAM;AAAA,MAAG;AAAA,IACnD,GAAG,MAAM,GAAG,WAAY;AACpB,UAAI,OAAO,IAAI,OAAO;AACtB,kBAAY,MAAM,IAAI;AAAA,IAC1B,GAAG,EAAE;AAAA,EACT;AACA,SAAOA;AACX,EAAE;AAmBK,SAAS,WAAW,MAAM,KAAK;AAClC,SAAO,OAAO,IAAI,IAAI,GAAG,KAAK,SAAS,GAAG,EAAE,IAAI,GAAG;AACvD;AAQA,IAAI,aAA4B,WAAY;AAKxC,WAASC,YAAW,IAAI;AACpB,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,SAAS;AAAA,EAClB;AAMA,EAAAA,YAAW,UAAU,OAAO,SAAU,OAAO,OAAO;AAChD,QAAI,CAAC,KAAK;AACN,YAAM;AACV,QAAI,CAAC,KAAK,GAAG;AACT,UAAI,KAAK,KAAK,KAAK,EAAE,QAAQ;AACzB,YAAI,IAAI,IAAI,GAAG,KAAK,EAAE,SAAS,MAAM,MAAM;AAC3C,UAAE,IAAI,KAAK,CAAC,GAAG,EAAE,IAAI,OAAO,KAAK,EAAE,MAAM;AAAA,MAC7C;AAEI,aAAK,IAAI;AACb,UAAI,KAAK,EAAE,SAAS,GAAG;AACnB,YAAI,UAAU;AACd,YAAI,KAAK,WAAY;AAAE,kBAAQ,OAAO,MAAM,SAAS,SAAS;AAAA,QAAG;AACjE,aAAK,IAAK,KAAK,EAAE,CAAC,KAAK,MAAM,KAAK,EAAE,CAAC,KAAK,OAAO,KAAK,EAAE,CAAC,KAAK,IACxD,IAAI,KAAK,EAAE,EAAE,KACX,KAAK,EAAE,CAAC,IAAI,OAAO,KAAM,KAAK,EAAE,CAAC,KAAK,IAAK,MAAO,KAAK,EAAE,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC,KAAK,KAC9E,IAAI,KAAK,EAAE,EAAE,IACb,IAAI,KAAK,EAAE,EAAE;AACvB,aAAK,EAAE,KAAK,KAAK,GAAG,KAAK;AACzB,aAAK,IAAI;AAAA,MACb;AAAA,IACJ;AAEI,WAAK,EAAE,KAAK,OAAO,KAAK;AAAA,EAChC;AACA,SAAOA;AACX,EAAE;AAKF,IAAI,kBAAiC,WAAY;AAK7C,WAASC,iBAAgB,IAAI;AACzB,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,SAAS;AAAA,EAClB;AAMA,EAAAA,iBAAgB,UAAU,OAAO,SAAU,OAAO,OAAO;AACrD,eAAW,UAAU,KAAK,KAAK,MAAM,OAAO,KAAK;AAAA,EACrD;AACA,SAAOA;AACX,EAAE;AAuCF,IAAI,KAAK,OAAO,eAAe,eAA6B,IAAI,YAAY;AAE5E,IAAI,KAAK,OAAO,eAAe,eAA6B,IAAI,YAAY;AAE5E,IAAI,MAAM;AACV,IAAI;AACA,KAAG,OAAO,IAAI,EAAE,QAAQ,KAAK,CAAC;AAC9B,QAAM;AACV,SACO,GAAG;AAAE;AAEZ,IAAI,QAAQ,SAAU,GAAG;AACrB,WAAS,IAAI,IAAI,IAAI,OAAK;AACtB,QAAI,IAAI,EAAE,GAAG;AACb,QAAI,MAAM,IAAI,QAAQ,IAAI,QAAQ,IAAI;AACtC,QAAI,IAAI,KAAK,EAAE;AACX,aAAO,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;AAC5B,QAAI,CAAC;AACD,WAAK,OAAO,aAAa,CAAC;AAAA,aACrB,MAAM,GAAG;AACd,YAAM,IAAI,OAAO,MAAM,EAAE,GAAG,IAAI,OAAO,MAAM,EAAE,GAAG,IAAI,OAAO,IAAK,EAAE,GAAG,IAAI,MAAO,OAC9E,KAAK,OAAO,aAAa,QAAS,KAAK,IAAK,QAAS,IAAI,IAAK;AAAA,IACtE,WACS,KAAK;AACV,WAAK,OAAO,cAAc,IAAI,OAAO,IAAK,EAAE,GAAG,IAAI,EAAG;AAAA;AAEtD,WAAK,OAAO,cAAc,IAAI,OAAO,MAAM,EAAE,GAAG,IAAI,OAAO,IAAK,EAAE,GAAG,IAAI,EAAG;AAAA,EACpF;AACJ;AAIA,IAAI,aAA4B,WAAY;AAKxC,WAASC,YAAW,IAAI;AACpB,SAAK,SAAS;AACd,QAAI;AACA,WAAK,IAAI,IAAI,YAAY;AAAA;AAEzB,WAAK,IAAI;AAAA,EACjB;AAMA,EAAAA,YAAW,UAAU,OAAO,SAAU,OAAO,OAAO;AAChD,QAAI,CAAC,KAAK;AACN,YAAM;AACV,YAAQ,CAAC,CAAC;AACV,QAAI,KAAK,GAAG;AACR,WAAK,OAAO,KAAK,EAAE,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC,GAAG,KAAK;AACzD,UAAI,OAAO;AACP,YAAI,KAAK,EAAE,OAAO,EAAE;AAChB,gBAAM;AACV,aAAK,IAAI;AAAA,MACb;AACA;AAAA,IACJ;AACA,QAAI,CAAC,KAAK;AACN,YAAM;AACV,QAAI,MAAM,IAAI,GAAG,KAAK,EAAE,SAAS,MAAM,MAAM;AAC7C,QAAI,IAAI,KAAK,CAAC;AACd,QAAI,IAAI,OAAO,KAAK,EAAE,MAAM;AAC5B,QAAIC,MAAK,MAAM,GAAG,GAAGC,MAAKD,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC;AAC1C,QAAI,OAAO;AACP,UAAI,GAAG;AACH,cAAM;AACV,WAAK,IAAI;AAAA,IACb;AAEI,WAAK,IAAI;AACb,SAAK,OAAOC,KAAI,KAAK;AAAA,EACzB;AACA,SAAOF;AACX,EAAE;AAKF,IAAI,aAA4B,WAAY;AAKxC,WAASG,YAAW,IAAI;AACpB,SAAK,SAAS;AAAA,EAClB;AAMA,EAAAA,YAAW,UAAU,OAAO,SAAU,OAAO,OAAO;AAChD,QAAI,CAAC,KAAK;AACN,YAAM;AACV,QAAI,KAAK;AACL,YAAM;AACV,SAAK,OAAO,QAAQ,KAAK,GAAG,KAAK,IAAI,SAAS,KAAK;AAAA,EACvD;AACA,SAAOA;AACX,EAAE;AASK,SAAS,QAAQ,KAAK,QAAQ;AACjC,MAAI,QAAQ;AACR,QAAI,OAAO,IAAI,GAAG,IAAI,MAAM;AAC5B,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE;AAC9B,WAAK,CAAC,IAAI,IAAI,WAAW,CAAC;AAC9B,WAAO;AAAA,EACX;AACA,MAAI;AACA,WAAO,GAAG,OAAO,GAAG;AACxB,MAAI,IAAI,IAAI;AACZ,MAAI,KAAK,IAAI,GAAG,IAAI,UAAU,IAAI,UAAU,EAAE;AAC9C,MAAI,KAAK;AACT,MAAI,IAAI,SAAU,GAAG;AAAE,OAAG,IAAI,IAAI;AAAA,EAAG;AACrC,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxB,QAAI,KAAK,IAAI,GAAG,QAAQ;AACpB,UAAI,IAAI,IAAI,GAAG,KAAK,KAAM,IAAI,KAAM,EAAE;AACtC,QAAE,IAAI,EAAE;AACR,WAAK;AAAA,IACT;AACA,QAAI,IAAI,IAAI,WAAW,CAAC;AACxB,QAAI,IAAI,OAAO;AACX,QAAE,CAAC;AAAA,aACE,IAAI;AACT,QAAE,MAAO,KAAK,CAAE,GAAG,EAAE,MAAO,IAAI,EAAG;AAAA,aAC9B,IAAI,SAAS,IAAI;AACtB,UAAI,SAAS,IAAI,QAAQ,MAAO,IAAI,WAAW,EAAE,CAAC,IAAI,MAClD,EAAE,MAAO,KAAK,EAAG,GAAG,EAAE,MAAQ,KAAK,KAAM,EAAG,GAAG,EAAE,MAAQ,KAAK,IAAK,EAAG,GAAG,EAAE,MAAO,IAAI,EAAG;AAAA;AAE7F,QAAE,MAAO,KAAK,EAAG,GAAG,EAAE,MAAQ,KAAK,IAAK,EAAG,GAAG,EAAE,MAAO,IAAI,EAAG;AAAA,EACtE;AACA,SAAO,IAAI,IAAI,GAAG,EAAE;AACxB;AAQO,SAAS,UAAU,KAAK,QAAQ;AACnC,MAAI,QAAQ;AACR,QAAI,IAAI;AACR,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,WAAK,OAAO,aAAa,MAAM,MAAM,IAAI,SAAS,GAAG,IAAI,KAAK,CAAC;AACnE,WAAO;AAAA,EACX,WACS;AACL,WAAO,GAAG,OAAO,GAAG;AAAA,OACnB;AACD,QAAIC,MAAK,MAAM,GAAG,GAAG,MAAMA,IAAG,CAAC,GAAG,MAAMA,IAAG,CAAC;AAC5C,QAAI,IAAI;AACJ,YAAM;AACV,WAAO;AAAA,EACX;AACJ;AAGA,IAAI,MAAM,SAAU,GAAG;AAAE,SAAO,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAAG;AAUzE,IAAI,OAAO,SAAU,GAAG,GAAG;AACvB,SAAO,GAAG,GAAG,CAAC,KAAK,GAAG,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC;AACtC;AACJ,SAAO,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;AACtD;AAEA,IAAI,OAAO,SAAU,IAAI;AACrB,MAAI,KAAK;AACT,MAAI,IAAI;AACJ,aAAS,KAAK,IAAI;AACd,UAAI,IAAI,GAAG,CAAC,EAAE;AACd,UAAI,IAAI;AACJ,cAAM;AACV,YAAM,IAAI;AAAA,IACd;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAI,MAAM,SAAU,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI;AAC3C,MAAIC,MAAK,GAAG,QAAQ,KAAK,EAAE,OAAO,MAAM,MAAM,GAAG;AACjD,MAAI,MAAM,KAAK,EAAE;AACjB,SAAO,GAAG,GAAG,MAAM,OAAO,WAAY,QAAS,GAAG,KAAK;AACvD,MAAI,MAAM;AACN,MAAE,GAAG,IAAI,IAAI,EAAE,GAAG,IAAI,EAAE;AAC5B,IAAE,CAAC,IAAI,IAAI,KAAK;AAChB,IAAE,GAAG,IAAK,EAAE,QAAQ,KAAM,KAAK,QAAQ,IAAI,EAAE,GAAG,IAAI,KAAK;AACzD,IAAE,GAAG,IAAI,EAAE,cAAc,KAAK,EAAE,GAAG,IAAI,EAAE,eAAe;AACxD,MAAI,KAAK,IAAI,KAAK,EAAE,SAAS,OAAO,KAAK,IAAI,IAAI,EAAE,KAAK,GAAG,IAAI,GAAG,YAAY,IAAI;AAClF,MAAI,IAAI,KAAK,IAAI;AACb,UAAM;AACV,SAAO,GAAG,GAAI,KAAK,KAAQ,GAAG,SAAS,IAAI,KAAM,KAAO,GAAG,QAAQ,KAAK,KAAO,GAAG,SAAS,KAAK,KAAO,GAAG,WAAW,KAAK,IAAM,GAAG,WAAW,MAAM,CAAE,GAAG,KAAK;AAC9J,MAAI,KAAK,MAAM;AACX,WAAO,GAAG,GAAG,EAAE,GAAG;AAClB,WAAO,GAAG,IAAI,GAAG,CAAC;AAClB,WAAO,GAAG,IAAI,GAAG,EAAE,IAAI;AAAA,EAC3B;AACA,SAAO,GAAG,IAAI,IAAIA,GAAE;AACpB,SAAO,GAAG,IAAI,IAAI,GAAG,GAAG,KAAK;AAC7B,MAAI,MAAM,MAAM;AACZ,WAAO,GAAG,GAAG,GAAG;AAChB,WAAO,GAAG,IAAI,GAAG,EAAE,KAAK;AACxB,WAAO,GAAG,IAAI,IAAI,EAAE,GAAG,KAAK;AAAA,EAChC;AACA,IAAE,IAAI,IAAI,CAAC;AACX,OAAKA;AACL,MAAI,KAAK;AACL,aAAS,KAAK,IAAI;AACd,UAAI,MAAM,GAAG,CAAC,GAAG,IAAI,IAAI;AACzB,aAAO,GAAG,GAAG,CAAC,CAAC;AACf,aAAO,GAAG,IAAI,GAAG,CAAC;AAClB,QAAE,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI;AAAA,IAChC;AAAA,EACJ;AACA,MAAI;AACA,MAAE,IAAI,IAAI,CAAC,GAAG,KAAK;AACvB,SAAO;AACX;AAEA,IAAI,MAAM,SAAU,GAAG,GAAG,GAAG,GAAG,GAAG;AAC/B,SAAO,GAAG,GAAG,SAAS;AACtB,SAAO,GAAG,IAAI,GAAG,CAAC;AAClB,SAAO,GAAG,IAAI,IAAI,CAAC;AACnB,SAAO,GAAG,IAAI,IAAI,CAAC;AACnB,SAAO,GAAG,IAAI,IAAI,CAAC;AACvB;AAIA,IAAI,iBAAgC,WAAY;AAK5C,WAASC,gBAAe,UAAU;AAC9B,SAAK,WAAW;AAChB,SAAK,IAAI,IAAI;AACb,SAAK,OAAO;AACZ,SAAK,cAAc;AAAA,EACvB;AASA,EAAAA,gBAAe,UAAU,UAAU,SAAU,OAAO,OAAO;AACvD,SAAK,OAAO,MAAM,OAAO,KAAK;AAAA,EAClC;AAQA,EAAAA,gBAAe,UAAU,OAAO,SAAU,OAAO,OAAO;AACpD,QAAI,CAAC,KAAK;AACN,YAAM;AACV,SAAK,EAAE,EAAE,KAAK;AACd,SAAK,QAAQ,MAAM;AACnB,QAAI;AACA,WAAK,MAAM,KAAK,EAAE,EAAE;AACxB,SAAK,QAAQ,OAAO,SAAS,KAAK;AAAA,EACtC;AACA,SAAOA;AACX,EAAE;AAOF,IAAI,aAA4B,WAAY;AAMxC,WAASC,YAAW,UAAU,MAAM;AAChC,QAAI,UAAU;AACd,QAAI,CAAC;AACD,aAAO,CAAC;AACZ,mBAAe,KAAK,MAAM,QAAQ;AAClC,SAAK,IAAI,IAAI,QAAQ,MAAM,SAAU,KAAK,OAAO;AAC7C,cAAQ,OAAO,MAAM,KAAK,KAAK;AAAA,IACnC,CAAC;AACD,SAAK,cAAc;AACnB,SAAK,OAAO,IAAI,KAAK,KAAK;AAAA,EAC9B;AACA,EAAAA,YAAW,UAAU,UAAU,SAAU,OAAO,OAAO;AACnD,QAAI;AACA,WAAK,EAAE,KAAK,OAAO,KAAK;AAAA,IAC5B,SACO,GAAG;AACN,WAAK,OAAO,GAAG,MAAM,KAAK;AAAA,IAC9B;AAAA,EACJ;AAMA,EAAAA,YAAW,UAAU,OAAO,SAAU,OAAO,OAAO;AAChD,mBAAe,UAAU,KAAK,KAAK,MAAM,OAAO,KAAK;AAAA,EACzD;AACA,SAAOA;AACX,EAAE;AAKF,IAAI,kBAAiC,WAAY;AAM7C,WAASC,iBAAgB,UAAU,MAAM;AACrC,QAAI,UAAU;AACd,QAAI,CAAC;AACD,aAAO,CAAC;AACZ,mBAAe,KAAK,MAAM,QAAQ;AAClC,SAAK,IAAI,IAAI,aAAa,MAAM,SAAU,KAAK,KAAK,OAAO;AACvD,cAAQ,OAAO,KAAK,KAAK,KAAK;AAAA,IAClC,CAAC;AACD,SAAK,cAAc;AACnB,SAAK,OAAO,IAAI,KAAK,KAAK;AAC1B,SAAK,YAAY,KAAK,EAAE;AAAA,EAC5B;AACA,EAAAA,iBAAgB,UAAU,UAAU,SAAU,OAAO,OAAO;AACxD,SAAK,EAAE,KAAK,OAAO,KAAK;AAAA,EAC5B;AAMA,EAAAA,iBAAgB,UAAU,OAAO,SAAU,OAAO,OAAO;AACrD,mBAAe,UAAU,KAAK,KAAK,MAAM,OAAO,KAAK;AAAA,EACzD;AACA,SAAOA;AACX,EAAE;AAMF,IAAI,MAAqB,WAAY;AAMjC,WAASC,KAAI,IAAI;AACb,SAAK,SAAS;AACd,SAAK,IAAI,CAAC;AACV,SAAK,IAAI;AAAA,EACb;AAKA,EAAAA,KAAI,UAAU,MAAM,SAAU,MAAM;AAChC,QAAI,UAAU;AACd,QAAI,KAAK,IAAI;AACT,YAAM;AACV,QAAI,IAAI,QAAQ,KAAK,QAAQ,GAAGC,MAAK,EAAE;AACvC,QAAI,MAAM,KAAK,SAAS,IAAI,OAAO,QAAQ,GAAG;AAC9C,QAAI,IAAIA,OAAM,KAAK,SAAS,UAAW,KAAM,IAAI,UAAU,EAAE;AAC7D,QAAI,KAAKA,MAAK,KAAK,KAAK,KAAK,IAAI;AACjC,QAAIA,MAAK;AACL,YAAM;AACV,QAAI,SAAS,IAAI,GAAG,EAAE;AACtB,QAAI,QAAQ,GAAG,MAAM,GAAG,CAAC;AACzB,QAAI,OAAO,CAAC,MAAM;AAClB,QAAI,OAAO,WAAY;AACnB,eAAS,KAAK,GAAG,SAAS,MAAM,KAAK,OAAO,QAAQ,MAAM;AACtD,YAAI,MAAM,OAAO,EAAE;AACnB,gBAAQ,OAAO,MAAM,KAAK,KAAK;AAAA,MACnC;AACA,aAAO,CAAC;AAAA,IACZ;AACA,QAAI,KAAK,KAAK;AACd,SAAK,IAAI;AACT,QAAI,MAAM,KAAK,EAAE;AACjB,QAAI,KAAK,IAAI,MAAM;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG,WAAY;AACX,YAAI,KAAK;AACL,eAAK,UAAU;AAAA,MACvB;AAAA,MACA,GAAG,WAAY;AACX,aAAK;AACL,YAAI,IAAI;AACJ,cAAI,MAAM,QAAQ,EAAE,MAAM,CAAC;AAC3B,cAAI;AACA,gBAAI,EAAE;AAAA;AAEN,oBAAQ,IAAI;AAAA,QACpB;AACA,aAAK;AAAA,MACT;AAAA,IACJ,CAAC;AACD,QAAI,KAAK;AACT,SAAK,SAAS,SAAU,KAAK,KAAK,OAAO;AACrC,UAAI,KAAK;AACL,gBAAQ,OAAO,KAAK,KAAK,KAAK;AAC9B,gBAAQ,UAAU;AAAA,MACtB,OACK;AACD,cAAM,IAAI;AACV,aAAK,KAAK,GAAG;AACb,YAAI,OAAO;AACP,cAAI,KAAK,IAAI,GAAG,EAAE;AAClB,iBAAO,IAAI,GAAG,SAAS;AACvB,iBAAO,IAAI,GAAG,KAAK,GAAG;AACtB,iBAAO,IAAI,GAAG,EAAE;AAChB,iBAAO,IAAI,IAAI,KAAK,IAAI;AACxB,eAAK,KAAK,EAAE;AACZ,aAAG,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,GAAG,MAAM,KAAK,KAAK,GAAG,OAAO,KAAK;AAClE,cAAI;AACA,eAAG,EAAE;AACT,eAAK;AAAA,QACT,WACS;AACL,eAAK;AAAA,MACb;AAAA,IACJ;AACA,SAAK,EAAE,KAAK,EAAE;AAAA,EAClB;AAMA,EAAAD,KAAI,UAAU,MAAM,WAAY;AAC5B,QAAI,UAAU;AACd,QAAI,KAAK,IAAI,GAAG;AACZ,UAAI,KAAK,IAAI;AACT,cAAM;AACV,YAAM;AAAA,IACV;AACA,QAAI,KAAK;AACL,WAAK,EAAE;AAAA;AAEP,WAAK,EAAE,KAAK;AAAA,QACR,GAAG,WAAY;AACX,cAAI,EAAE,QAAQ,IAAI;AACd;AACJ,kBAAQ,EAAE,OAAO,IAAI,CAAC;AACtB,kBAAQ,EAAE;AAAA,QACd;AAAA,QACA,GAAG,WAAY;AAAA,QAAE;AAAA,MACrB,CAAC;AACL,SAAK,IAAI;AAAA,EACb;AACA,EAAAA,KAAI,UAAU,IAAI,WAAY;AAC1B,QAAI,KAAK,GAAG,IAAI,GAAG,KAAK;AACxB,aAAS,KAAK,GAAGE,MAAK,KAAK,GAAG,KAAKA,IAAG,QAAQ,MAAM;AAChD,UAAI,IAAIA,IAAG,EAAE;AACb,YAAM,KAAK,EAAE,EAAE,SAAS,KAAK,EAAE,KAAK,KAAK,EAAE,IAAI,EAAE,EAAE,SAAS;AAAA,IAChE;AACA,QAAI,MAAM,IAAI,GAAG,KAAK,EAAE;AACxB,aAASC,MAAK,GAAG,KAAK,KAAK,GAAGA,MAAK,GAAG,QAAQA,OAAM;AAChD,UAAI,IAAI,GAAGA,GAAE;AACb,UAAI,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;AACrC,YAAM,KAAK,EAAE,EAAE,SAAS,KAAK,EAAE,KAAK,KAAK,EAAE,IAAI,EAAE,EAAE,SAAS,IAAI,KAAK,EAAE;AAAA,IAC3E;AACA,QAAI,KAAK,IAAI,KAAK,EAAE,QAAQ,IAAI,CAAC;AACjC,SAAK,OAAO,MAAM,KAAK,IAAI;AAC3B,SAAK,IAAI;AAAA,EACb;AAKA,EAAAH,KAAI,UAAU,YAAY,WAAY;AAClC,aAAS,KAAK,GAAGE,MAAK,KAAK,GAAG,KAAKA,IAAG,QAAQ,MAAM;AAChD,UAAI,IAAIA,IAAG,EAAE;AACb,QAAE,EAAE;AAAA,IACR;AACA,SAAK,IAAI;AAAA,EACb;AACA,SAAOF;AACX,EAAE;AAgJF,IAAI,mBAAkC,WAAY;AAC9C,WAASI,oBAAmB;AAAA,EAC5B;AACA,EAAAA,kBAAiB,UAAU,OAAO,SAAU,MAAM,OAAO;AACrD,SAAK,OAAO,MAAM,MAAM,KAAK;AAAA,EACjC;AACA,EAAAA,kBAAiB,cAAc;AAC/B,SAAOA;AACX,EAAE;AAMF,IAAI,eAA8B,WAAY;AAI1C,WAASC,gBAAe;AACpB,QAAI,UAAU;AACd,SAAK,IAAI,IAAI,QAAQ,SAAU,KAAK,OAAO;AACvC,cAAQ,OAAO,MAAM,KAAK,KAAK;AAAA,IACnC,CAAC;AAAA,EACL;AACA,EAAAA,cAAa,UAAU,OAAO,SAAU,MAAM,OAAO;AACjD,QAAI;AACA,WAAK,EAAE,KAAK,MAAM,KAAK;AAAA,IAC3B,SACO,GAAG;AACN,WAAK,OAAO,GAAG,MAAM,KAAK;AAAA,IAC9B;AAAA,EACJ;AACA,EAAAA,cAAa,cAAc;AAC3B,SAAOA;AACX,EAAE;AAKF,IAAI,oBAAmC,WAAY;AAI/C,WAASC,mBAAkB,GAAG,IAAI;AAC9B,QAAI,UAAU;AACd,QAAI,KAAK,MAAQ;AACb,WAAK,IAAI,IAAI,QAAQ,SAAU,KAAK,OAAO;AACvC,gBAAQ,OAAO,MAAM,KAAK,KAAK;AAAA,MACnC,CAAC;AAAA,IACL,OACK;AACD,WAAK,IAAI,IAAI,aAAa,SAAU,KAAK,KAAK,OAAO;AACjD,gBAAQ,OAAO,KAAK,KAAK,KAAK;AAAA,MAClC,CAAC;AACD,WAAK,YAAY,KAAK,EAAE;AAAA,IAC5B;AAAA,EACJ;AACA,EAAAA,mBAAkB,UAAU,OAAO,SAAU,MAAM,OAAO;AACtD,QAAI,KAAK,EAAE;AACP,aAAO,IAAI,MAAM,CAAC;AACtB,SAAK,EAAE,KAAK,MAAM,KAAK;AAAA,EAC3B;AACA,EAAAA,mBAAkB,cAAc;AAChC,SAAOA;AACX,EAAE;AAKF,IAAI,QAAuB,WAAY;AAKnC,WAASC,OAAM,IAAI;AACf,SAAK,SAAS;AACd,SAAK,IAAI,CAAC;AACV,SAAK,IAAI;AAAA,MACL,GAAG;AAAA,IACP;AACA,SAAK,IAAI;AAAA,EACb;AAMA,EAAAA,OAAM,UAAU,OAAO,SAAU,OAAO,OAAO;AAC3C,QAAI,UAAU;AACd,QAAI,CAAC,KAAK;AACN,YAAM;AACV,QAAI,CAAC,KAAK;AACN,YAAM;AACV,QAAI,KAAK,IAAI,GAAG;AACZ,UAAI,MAAM,KAAK,IAAI,KAAK,GAAG,MAAM,MAAM;AACvC,UAAI,QAAQ,MAAM,SAAS,GAAG,GAAG;AACjC,WAAK,KAAK;AACV,UAAI,KAAK;AACL,aAAK,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC;AAAA;AAE1B,aAAK,EAAE,CAAC,EAAE,KAAK,KAAK;AACxB,cAAQ,MAAM,SAAS,GAAG;AAC1B,UAAI,MAAM;AACN,eAAO,KAAK,KAAK,OAAO,KAAK;AAAA,IACrC,OACK;AACD,UAAI,IAAI,GAAG,IAAI,GAAG,KAAK,QAAQ,MAAM;AACrC,UAAI,CAAC,KAAK,EAAE;AACR,cAAM;AAAA,eACD,CAAC,MAAM;AACZ,cAAM,KAAK;AAAA,WACV;AACD,cAAM,IAAI,GAAG,KAAK,EAAE,SAAS,MAAM,MAAM;AACzC,YAAI,IAAI,KAAK,CAAC,GAAG,IAAI,IAAI,OAAO,KAAK,EAAE,MAAM;AAAA,MACjD;AACA,UAAI,IAAI,IAAI,QAAQ,KAAK,KAAK,GAAG,MAAM,MAAM,KAAK;AAClD,UAAI,UAAU,WAAY;AACtB,YAAIC;AACJ,YAAI,MAAM,GAAG,KAAK,CAAC;AACnB,YAAI,OAAO,UAAW;AAClB,cAAI,GAAG,KAAK;AACZ,iBAAO,IAAI;AACX,iBAAO,IAAI;AACX,cAAI,KAAK,GAAG,KAAK,IAAI,CAAC,GAAG,QAAQ,GAAG,KAAK,IAAI,CAAC,GAAG,IAAI,KAAK,MAAM,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,IAAI,EAAE,GAAG,KAAK,GAAG,KAAK,IAAI,EAAE;AACvH,cAAI,IAAI,IAAI,KAAK,MAAM,IAAI;AACvB,gBAAI,SAAS,CAAC;AACd,mBAAO,EAAE,QAAQ,MAAM;AACvB,gBAAI;AACJ,gBAAI,OAAO,GAAG,KAAK,IAAI,EAAE,GAAG,OAAO,GAAG,KAAK,IAAI,EAAE;AACjD,gBAAI,OAAO,UAAU,IAAI,SAAS,IAAI,IAAI,KAAK,KAAK,GAAG,GAAG,CAAC,CAAC;AAC5D,gBAAI,QAAQ,YAAY;AACpB,cAAAA,MAAK,KAAK,CAAC,EAAE,IAAI,KAAK,KAAK,CAAC,GAAG,OAAOA,IAAG,CAAC,GAAG,OAAOA,IAAG,CAAC;AAAA,YAC5D,WACS;AACL,qBAAO;AACX,iBAAK;AACL,mBAAO,IAAI;AACX,gBAAI;AACJ,gBAAI,SAAS;AAAA,cACT,MAAM;AAAA,cACN,aAAa;AAAA,cACb,OAAO,WAAY;AACf,oBAAI,CAAC,OAAO;AACR,wBAAM;AACV,oBAAI,CAAC;AACD,yBAAO,OAAO,MAAM,IAAI,IAAI;AAAA,qBAC3B;AACD,sBAAI,MAAM,QAAQ,EAAE,KAAK;AACzB,sBAAI,CAAC;AACD,0BAAM,8BAA8B;AACxC,wBAAM,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,MAAM,IAAI;AACzD,sBAAI,SAAS,SAAU,KAAKC,MAAKC,QAAO;AAAE,2BAAO,OAAO,KAAKD,MAAKC,MAAK;AAAA,kBAAG;AAC1E,2BAAS,KAAK,GAAG,SAAS,QAAQ,KAAK,OAAO,QAAQ,MAAM;AACxD,wBAAID,OAAM,OAAO,EAAE;AACnB,wBAAI,KAAKA,MAAK,KAAK;AAAA,kBACvB;AACA,sBAAI,QAAQ,EAAE,CAAC,KAAK,UAAU,QAAQ;AAClC,4BAAQ,IAAI;AAAA;AAEZ,wBAAI,KAAK,IAAI,IAAI;AAAA,gBACzB;AAAA,cACJ;AAAA,cACA,WAAW,WAAY;AACnB,oBAAI,OAAO,IAAI;AACX,sBAAI,UAAU;AAAA,cACtB;AAAA,YACJ;AACA,gBAAI,QAAQ;AACR,qBAAO,OAAO,MAAM,OAAO,eAAe;AAC9C,mBAAO,OAAO,MAAM;AAAA,UACxB;AACA,iBAAO;AAAA,QACX,WACS,IAAI;AACT,cAAI,OAAO,WAAW;AAClB,iBAAK,KAAK,MAAM,MAAM,MAAM,IAAI,IAAI,GAAG,OAAO,IAAI;AAClD,mBAAO;AAAA,UACX,WACS,OAAO,UAAW;AACvB,iBAAK,KAAK,GAAG,IAAI,GAAG,OAAO,IAAI;AAC/B,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,SAAS;AACb,aAAO,IAAI,IAAI,GAAG,EAAE,GAAG;AACnB,YAAI,UAAU,QAAQ;AACtB,YAAI,YAAY;AACZ;AAAA,MACR;AACA,WAAK,IAAI;AACT,UAAI,KAAK,GAAG;AACR,YAAI,MAAM,IAAI,IAAI,SAAS,GAAG,KAAK,MAAM,MAAM,MAAM,MAAM,GAAG,KAAK,KAAK,EAAE,KAAK,aAAa,EAAE,IAAI,IAAI,SAAS,GAAG,CAAC;AACnH,YAAI;AACA,cAAI,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA;AAEjB,eAAK,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,GAAG;AAAA,MAClC;AACA,UAAI,IAAI;AACJ,eAAO,KAAK,KAAK,IAAI,SAAS,CAAC,GAAG,KAAK;AAC3C,WAAK,IAAI,IAAI,SAAS,CAAC;AAAA,IAC3B;AACA,QAAI,OAAO;AACP,UAAI,KAAK;AACL,cAAM;AACV,WAAK,IAAI;AAAA,IACb;AAAA,EACJ;AAMA,EAAAF,OAAM,UAAU,WAAW,SAAU,SAAS;AAC1C,SAAK,EAAE,QAAQ,WAAW,IAAI;AAAA,EAClC;AACA,SAAOA;AACX,EAAE;", "names": ["l", "_a", "_b", "i", "td", "_a", "fl", "Deflate", "AsyncDeflate", "Inflate", "AsyncInflate", "Gzip", "<PERSON><PERSON><PERSON>", "AsyncGunzip", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "AsyncUnzlib", "Decompress", "AsyncDecompress", "DecodeUTF8", "_a", "ch", "EncodeUTF8", "_a", "fl", "ZipPassThrough", "ZipDeflate", "AsyncZipDeflate", "Zip", "fl", "_a", "_b", "UnzipPassThrough", "UnzipInflate", "AsyncUnzipInflate", "Unzip", "_a", "dat", "final"]}